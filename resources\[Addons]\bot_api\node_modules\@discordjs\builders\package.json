{"name": "@discordjs/builders", "version": "0.11.0", "description": "A set of builders that you can use when creating your bot", "scripts": {"pretest": "npm run build", "test": "jest --pass-with-no-tests", "test:ci": "jest --no-stack-trace --verbose --pass-with-no-tests", "prebuild": "npm run lint", "build": "tsup", "lint": "eslint src --ext mjs,js,ts", "lint:fix": "eslint src --ext mjs,js,ts --fix", "format": "prettier --write **/*.{ts,js,json,yml,yaml}", "prepare": "is-ci || husky install", "docs": "typedoc --json docs/typedoc-out.json src/index.ts && node scripts/docs.mjs", "prepublishOnly": "npm run lint && npm run test", "release": "standard-version --preset angular"}, "main": "./dist/index.js", "module": "./dist/index.mjs", "typings": "./dist/index.d.ts", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "directories": {"lib": "src", "test": "__tests__"}, "files": ["dist"], "contributors": ["<PERSON> <<EMAIL>>", "Crawl <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "SpaceEEC <<EMAIL>>"], "license": "Apache-2.0", "keywords": ["discord", "api", "bot", "client", "node", "discordapp", "discordjs"], "repository": {"type": "git", "url": "https://github.com/discordjs/builders.git"}, "bugs": {"url": "https://github.com/discordjs/builders/issues"}, "homepage": "https://github.com/discordjs/builders", "dependencies": {"@sindresorhus/is": "^4.2.0", "discord-api-types": "^0.26.0", "ts-mixer": "^6.0.0", "tslib": "^2.3.1", "zod": "^3.11.6"}, "devDependencies": {"@babel/core": "^7.16.5", "@babel/plugin-proposal-decorators": "^7.16.5", "@babel/preset-env": "^7.16.5", "@babel/preset-typescript": "^7.16.5", "@commitlint/cli": "^15.0.0", "@commitlint/config-angular": "^15.0.0", "@discordjs/ts-docgen": "^0.3.4", "@types/jest": "^27.0.3", "@types/node": "^16.11.6", "@typescript-eslint/eslint-plugin": "^5.8.0", "@typescript-eslint/parser": "^5.8.0", "babel-plugin-transform-typescript-metadata": "^0.3.2", "eslint": "^8.5.0", "eslint-config-marine": "^9.1.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^7.0.4", "is-ci": "^3.0.1", "jest": "^27.4.5", "lint-staged": "^12.1.4", "prettier": "^2.5.1", "standard-version": "^9.3.2", "tsup": "^5.11.8", "typedoc": "^0.22.10", "typescript": "^4.5.4"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}, "publishConfig": {"access": "public"}}