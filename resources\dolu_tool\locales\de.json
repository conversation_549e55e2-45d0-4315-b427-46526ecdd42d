{"cannot_goback": "<PERSON>ine vorherige Position gefunden. Du musst dich zuerst irgendwohin teleportieren", "no_marker": "Du hast keinen Mark<PERSON> gesetzt", "command_tpm": "%s Teleportiere zum Marker", "command_noclip": "%s <PERSON><PERSON><PERSON>", "command_openui": "%s <PERSON><PERSON><PERSON>", "command_weather_notfound": "<PERSON>s wurde versucht, einen ungültigen Wettertyp zu setzen: %s", "teleport_invalid_coords": "<PERSON><PERSON><PERSON>, einen S<PERSON>ler zu ungültigen Koordinaten zu teleportieren", "model_doesnt_exist": "Model %s existiert nicht", "copied_coords_clipboard": "Koordinaten in die Zwischenablage kopiert", "copied_model_clipboard": "Model Hash in die Zwischenablage kopiert", "press_escape_exit": "<PERSON><PERSON><PERSON> 'Escape', um den Bearbeitungsmodus zu verlassen", "custom_location_created": "Benutzerdefinierter Ort erfolgreich erstellt", "vehicle_upgraded": "Fahrzeug erfolgreich aufgerüstet!", "weapon_gave": "Du hast eine Waffe erhalten", "weapon_cant_carry": "Du kannst diese Waffe nicht tragen", "max_health_set": "Maximale Gesundheit erfolgreich festgelegt", "entity_cant_be_loaded": "Die Entität kann nicht geladen werden...", "entity_doesnt_exist": "Entität existiert nicht", "entity_deleted": "Entität erfolgreich gelöscht", "teleport_success": "Erfolgreich teleportiert. Benutze /goback, um zur letzten Position zurückzukehren", "ui_home": "Startseite", "ui_world": "Welt", "ui_exit": "<PERSON>den", "ui_copy_coords": "Koordinaten kopieren", "ui_copied_coords": "Koordinaten kopiert", "ui_copy_name": "<PERSON>n kopieren", "ui_copied_name": "Name kopiert", "ui_copy_hash": "<PERSON>h kopieren", "ui_copied_hash": "<PERSON><PERSON> kop<PERSON>t", "ui_name": "Name", "ui_hash": "Hash", "ui_coords": "Koordinaten", "ui_heading": "Blickrichtung", "ui_interior_id": "Interior ID", "ui_current_room": "Aktueller Raum", "ui_teleport": "Teleportieren", "ui_not_in_interior": "Du befindest dich nicht in einem Interior", "ui_no_last_location": "Du hast dich noch nicht zu einem Ort teleportiert", "ui_current_coords": "Aktuelle Koordinaten", "ui_set_coords": "Koordinaten setzen", "ui_save_location": "Als Ort s<PERSON>ichern", "ui_last_location": "Letzte Position", "ui_current_interior": "Aktuelles Interior", "ui_quick_actions": "Schnellaktionen", "ui_clean_zone": "Zone säubern", "ui_clean_ped": "<PERSON><PERSON>", "ui_upgrade_vehicle": "Fahrzeug aufrüsten", "ui_repair_vehicle": "Fahrzeug reparieren", "ui_delete_vehicle": "Fahrzeug löschen", "ui_set_sunny_day": "Sonniger Tag einstellen", "ui_spawn_vehicle": "Fahrzeug spawnen", "ui_max_health": "Maximale Gesundheit", "ui_time_freeze": "Zeit einfrieren", "ui_time_not_freeze": "Zeit nicht einfrieren", "ui_time": "Zeit", "ui_sync": "Synchroni<PERSON><PERSON>", "ui_freeze_time": "Zeit einfrieren", "ui_weather": "Wetter", "ui_choose_weather": "<PERSON><PERSON><PERSON><PERSON> einen Wettertyp", "ui_current_weather": "Aktuelles Wetter?", "ui_freeze_weather": "<PERSON>ter ein<PERSON>rieren", "ui_interior": "Interior", "ui_room_count": "<PERSON><PERSON>", "ui_portal_count": "<PERSON> Anzahl", "ui_portals": "Portale", "ui_infos": "Informationen", "ui_fill_portals": "<PERSON><PERSON><PERSON>", "ui_outline_portals": "<PERSON><PERSON><PERSON>", "ui_corcers_portals": "<PERSON><PERSON>n", "ui_flag": "Flagge", "ui_room_from": "<PERSON><PERSON> von", "ui_room_to": "<PERSON><PERSON> zu", "ui_index": "Index", "ui_timecycle": "Zeitzyklus", "ui_no_timecycle_found": "<PERSON><PERSON> gefunden", "ui_object_spawner": "Objekt-Spawner", "ui_locations": "Orte", "ui_snap_to_ground": "An Boden anpassen", "ui_duplicate": "Duplizieren", "ui_no_location_found": "<PERSON><PERSON> gefunden", "ui_goto": "<PERSON><PERSON><PERSON> zu", "ui_show_custom_locations": "Benutzerdefinierte Orte anzeigen", "ui_show_vanilla_locations": "Vanilla-<PERSON><PERSON> anzeigen", "ui_create_custom_location": "Benutzerdefinierten Ort erstellen", "ui_search": "<PERSON><PERSON>", "ui_rename": "Umbenennen", "ui_delete": "Löschen", "ui_vanilla": "Vanilla", "ui_custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ui_peds": "Pedestrians", "ui_no_ped_found": "<PERSON><PERSON> gefunden", "ui_set_by_name": "<PERSON><PERSON>zen", "ui_set_ped": "<PERSON><PERSON><PERSON>", "ui_vehicles": "Fahrzeuge", "ui_spawn": "Spawnen", "ui_spawn_by_name": "Nach Namen spawnen", "ui_no_vehicle_found": "<PERSON><PERSON> gefunden", "ui_weapons": "Waffen", "ui_give_weapon_by_name": "Waffe nach Namen geben", "ui_give_weapon": "<PERSON><PERSON><PERSON>", "ui_no_weapon_found": "<PERSON><PERSON> gefunden", "ui_set_coords_as_string": "Als String setzen", "ui_set_coords_separate": "Als getrennte Werte setzen", "ui_confirm": "Bestätigen", "ui_cancel": "Abbrechen", "ui_location_name": "Ortsname", "ui_create_location_description": "Speichert deine aktuellen Koordinaten und Blickrichtung", "ui_add_entity": "Neue Entität hinzufügen", "ui_add_entity_description": "<PERSON><PERSON> den Namen der Entität ein", "ui_delete_all_entities": "Alle gespawnten Entitäten löschen?", "ui_amount": "<PERSON><PERSON><PERSON>", "ui_portal_flag_1": "Deaktiviert die Außenanzeige", "ui_portal_flag_2": "Deaktiviert die Innenanzeige", "ui_portal_flag_4": "Spiegelung", "ui_portal_flag_8": "Zusätzlicher Bloom", "ui_portal_flag_16": "Unbekannt 16", "ui_portal_flag_32": "Verwende Außen-LOD", "ui_portal_flag_64": "Verstecke bei geschlossener Tür", "ui_portal_flag_128": "Unbekannt 128", "ui_portal_flag_256": "Spiegele Außenportale", "ui_portal_flag_512": "Unbekannt 512", "ui_portal_flag_1024": "Spiegele Limbo-Entitäten", "ui_portal_flag_2048": "Unbekannt 2048", "ui_portal_flag_4096": "Unbekannt 4096", "ui_portal_flag_8192": "Deaktiviere Farbschnittstelle", "ui_update_warning": "Update verfügbar!", "ui_github": "GitHub Repository öffnen", "ui_discord": "<PERSON><PERSON> beitreten", "ui_audio": "Audio", "ui_static_emitters": "Statische Emitter", "ui_draw_static_emitters": "Statische Emitter anzeigen", "ui_draw_distance": "Anzeigebereich", "ui_closest_emitter_info": "Nächster Emitter Info", "ui_refresh": "Aktualisieren", "ui_distance": "Entfernung", "ui_meters": "<PERSON>er", "ui_flags": "Flags", "ui_room": "<PERSON><PERSON>", "ui_radio_station": "Radiosender", "ui_copied_rotation": "Rotation kopiert", "ui_copy_rotation": "Rotation kopieren"}