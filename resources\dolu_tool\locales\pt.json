{"cannot_goback": "Sem localizacao anterior. Teletransporta-te primeiro", "no_marker": "Não definiste nenhum marcador", "command_tpm": "%s Teletransportar para o marcador", "command_noclip": "%s Alternar noclip", "command_openui": "%s Abrir a ferramenta", "command_weather_notfound": "A tentar definir um clima inválido: %s", "teleport_invalid_coords": "Coordenadas inválidas para teletransportar", "model_doesnt_exist": "Modelo %s não existe", "copied_coords_clipboard": "Coordenadas copiadas para o clipboard", "copied_model_clipboard": "Model hash copied to clipboard", "press_escape_exit": "Pressiona 'Escape' para sair do modo de edição", "custom_location_created": "Localização criada com sucesso", "vehicle_upgraded": "Veículo melhorado com sucesso!", "weapon_gave": "Recebeste uma arma", "weapon_cant_carry": "Não podes receber esta arma", "max_health_set": "Max health succefully set", "entity_cant_be_loaded": "A entity não pode ser carregada...", "entity_doesnt_exist": "A entity não existe", "entity_deleted": "Entity apagada com sucesso", "teleport_success": "Teletransportado com sucesso. Usa /goback para voltar ao local anterior", "ui_home": "Início", "ui_world": "Ambiente", "ui_exit": "<PERSON><PERSON>", "ui_copy_coords": "<PERSON><PERSON><PERSON>ord<PERSON>", "ui_copied_coords": "Coordenadas copiadas", "ui_copy_name": "Copiar nome", "ui_copied_name": "Nome copiado", "ui_copy_hash": "Copiar hash", "ui_copied_hash": "Hash copiada", "ui_name": "Nome", "ui_hash": "Hash", "ui_coords": "<PERSON><PERSON><PERSON><PERSON>", "ui_heading": "Direcção", "ui_interior_id": "Id do <PERSON>", "ui_current_room": "Divisão actual", "ui_teleport": "<PERSON>r", "ui_not_in_interior": "Não estás dentro de nenhum interior", "ui_no_last_location": "Ainda não te teleportaste para nenhum local", "ui_current_coords": "Coordenadas atuais", "ui_set_coords": "<PERSON><PERSON><PERSON> coordena<PERSON>", "ui_save_location": "Guardar localização", "ui_last_location": "Última localização", "ui_current_interior": "Current Interior", "ui_quick_actions": "Acções r<PERSON>", "ui_clean_zone": "Limpar zona", "ui_clean_ped": "Limpar peds", "ui_upgrade_vehicle": "<PERSON><PERSON><PERSON>", "ui_repair_vehicle": "<PERSON><PERSON><PERSON>", "ui_delete_vehicle": "<PERSON><PERSON><PERSON>", "ui_set_sunny_day": "Definir dia limpo", "ui_spawn_vehicle": "Spawn<PERSON> ve<PERSON>lo", "ui_max_health": "Vida máxima", "ui_time_freeze": "Tempo parado", "ui_time_not_freeze": "Tempo a correr", "ui_time": "Tempo", "ui_sync": "Sync", "ui_freeze_time": "Parar tempo", "ui_weather": "Clima", "ui_choose_weather": "Escolher um clima", "ui_current_weather": "Clima atual?", "ui_freeze_weather": "Congelar clima", "ui_interior": "Interior", "ui_room_count": "Número de divisões", "ui_portal_count": "Número de portais", "ui_portals": "Portais", "ui_infos": "Informações", "ui_fill_portals": "<PERSON><PERSON><PERSON>", "ui_outline_portals": "T<PERSON><PERSON><PERSON>", "ui_corcers_portals": "Cantos", "ui_flag": "Flag", "ui_room_from": "Da divisão", "ui_room_to": "Para a divisão", "ui_index": "<PERSON><PERSON><PERSON>", "ui_timecycle": "Timecycle", "ui_no_timecycle_found": "No timecycle found", "ui_object_spawner": "<PERSON><PERSON><PERSON>", "ui_locations": "Localizações", "ui_snap_to_ground": "Colocar no chão", "ui_duplicate": "Duplicar", "ui_no_location_found": "Nenhuma localização encontrada", "ui_goto": "<PERSON>r", "ui_show_custom_locations": "Localizações custom", "ui_show_vanilla_locations": "Localizações pré-definidas", "ui_create_custom_location": "Criar localização", "ui_search": "Procurar", "ui_rename": "<PERSON>dar nome", "ui_delete": "<PERSON><PERSON><PERSON>", "ui_vanilla": "Pré<PERSON><PERSON><PERSON><PERSON>", "ui_custom": "Custom", "ui_peds": "Peds", "ui_no_ped_found": "Nenhum ped encontrado", "ui_set_by_name": "Definir por nome", "ui_set_ped": "Aplicar", "ui_vehicles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ui_spawn": "Spawn", "ui_spawn_by_name": "Spawn por nome", "ui_no_vehicle_found": "<PERSON>enhum veí<PERSON>lo encontrado", "ui_weapons": "Armas", "ui_give_weapon_by_name": "Dar arma pelo nome", "ui_give_weapon": "Dar", "ui_no_weapon_found": "Nenhuma arma encontrada", "ui_set_coords_as_string": "Como string", "ui_set_coords_separate": "Como valores individuas", "ui_confirm": "Confirmar", "ui_cancel": "<PERSON><PERSON><PERSON>", "ui_location_name": "Nome da localização", "ui_create_location_description": "Vai guardar a tuas coordenadas e a tua direção atual", "ui_add_entity": "Adicionar uma entity nova", "ui_add_entity_description": "Introduz o nome da entity", "ui_delete_all_entities": "<PERSON><PERSON><PERSON> todas as entities criadas?", "ui_amount": "Quantidade", "ui_portal_flag_1": "Desativa o render do exterior", "ui_portal_flag_2": "Desativa o render do interior", "ui_portal_flag_4": "Mirror", "ui_portal_flag_8": "Extra bloom", "ui_portal_flag_16": "Unknown 16", "ui_portal_flag_32": "Usa o nível de detalhe (LOD) do exterior", "ui_portal_flag_64": "Esconde quando a porta fecha", "ui_portal_flag_128": "Unknown 128", "ui_portal_flag_256": "Mirror exterior portals", "ui_portal_flag_512": "Unknown 512", "ui_portal_flag_1024": "Mirror limbo entities", "ui_portal_flag_2048": "Unknown 2048", "ui_portal_flag_4096": "Unknown 4096", "ui_portal_flag_8192": "Disable farclipping", "ui_update_warning": "Actualização disponível!", "ui_github": "<PERSON><PERSON><PERSON>osit<PERSON><PERSON>", "ui_discord": "Entrar no Discord", "ui_audio": "<PERSON><PERSON><PERSON>", "ui_static_emitters": "Emissores estáticos", "ui_draw_static_emitters": "Mostrar emissores estáticos", "ui_draw_distance": "Distância de render", "ui_closest_emitter_info": "Info do emissores mais próximo", "ui_refresh": "<PERSON><PERSON><PERSON><PERSON>", "ui_distance": "Distância", "ui_meters": "metros", "ui_flags": "Flags", "ui_room": "Divisão", "ui_radio_station": "Estação de rádio", "ui_copied_rotation": "Copied rotation", "ui_copy_rotation": "Copy rotation"}