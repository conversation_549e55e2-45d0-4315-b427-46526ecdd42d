{"cannot_goback": "没有找到最后的位置数据, 你需要先传送至某个区域", "no_marker": "您没有设置任何导航点", "command_tpm": "%s 传送到导航点", "command_noclip": "%s 切换飞行模式", "command_openui": "%s 打开Dolu工具", "command_weather_notfound": "尝试设置无效的天气类型: %s", "teleport_invalid_coords": "尝试将玩家传送到无效的坐标类型", "model_doesnt_exist": "模型:%s并不存在", "copied_coords_clipboard": "坐标苏剧已复制到剪贴板", "copied_model_clipboard": "模型哈希值已复制到剪贴板", "press_escape_exit": "键下 'Escape' 退出编辑模式", "custom_location_created": "自定义位置数据已成功创建", "vehicle_upgraded": "车辆升级改装成功!", "weapon_gave": "你刚刚收到一件武器", "weapon_cant_carry": "你无法获得这把武器", "max_health_set": "已成功设置为最大生命值", "entity_cant_be_loaded": "该实体模型无法加载...", "entity_doesnt_exist": "该实体模型并不存在", "entity_deleted": "实体模型已陈工移除", "teleport_success": "传送成功. 可使用 /goback 命令返回上一个位置", "ui_home": "主页功能", "ui_world": "游戏世界", "ui_exit": "关闭", "ui_copy_coords": "复制坐标数据", "ui_copied_coords": "已复制", "ui_copy_name": "复制其名称", "ui_copied_name": "已复制", "ui_copy_hash": "复制其哈希值", "ui_copied_hash": "已复制", "ui_name": "名称", "ui_hash": "哈希值", "ui_coords": "坐标数据", "ui_heading": "朝向数据", "ui_interior_id": "内饰ID数据", "ui_current_room": "当前房间数据", "ui_teleport": "传送至", "ui_not_in_interior": "您尚未处于任一地图模组内饰中", "ui_no_last_location": "暂无最近一次传送的数据", "ui_current_coords": "当前坐标系", "ui_set_coords": "设置当前坐标", "ui_save_location": "保存当前坐标数据", "ui_last_location": "上一位置数据", "ui_current_interior": "当前地图模组数据", "ui_quick_actions": "快捷功能", "ui_clean_zone": "清理附近杂物", "ui_clean_ped": "清理附近模型", "ui_upgrade_vehicle": "改装载具", "ui_repair_vehicle": "维修载具", "ui_delete_vehicle": "移除载具", "ui_set_sunny_day": "配置天气:晴天", "ui_spawn_vehicle": "生成载具", "ui_max_health": "重置生命值", "ui_time_freeze": "时间状态:已锁定", "ui_time_not_freeze": "时间状态:未锁定", "ui_time": "世界时间", "ui_sync": "同步时间", "ui_freeze_time": "锁定时间", "ui_weather": "世界天气", "ui_choose_weather": "选择天气类型", "ui_current_weather": "当前天气?", "ui_freeze_weather": "锁定天气模式", "ui_interior": "地图模组数据", "ui_room_count": "房间数量", "ui_portal_count": "门窗数量", "ui_portals": "门窗数据", "ui_infos": "详情", "ui_fill_portals": "高亮", "ui_outline_portals": "轮廓线", "ui_corcers_portals": "边角数据", "ui_flag": "Flag属性", "ui_room_from": "属于房间:", "ui_room_to": "链接至房间:", "ui_index": "索引值", "ui_timecycle": "时间周期", "ui_no_timecycle_found": "暂无有效的时间周期数据", "ui_object_spawner": "物体生成器", "ui_locations": "位置坐标数据", "ui_snap_to_ground": "附于地面", "ui_duplicate": "复制", "ui_no_location_found": "无有效位置数据", "ui_goto": "传至其位置", "ui_show_custom_locations": "显示自定义坐标数据", "ui_show_vanilla_locations": "显示默认坐标数据", "ui_create_custom_location": "新建坐标数据", "ui_search": "检索", "ui_rename": "重命名", "ui_delete": "删除", "ui_vanilla": "默认", "ui_custom": "自定义", "ui_peds": "游戏模型", "ui_no_ped_found": "无有效的模型数据", "ui_set_by_name": "直接应用", "ui_set_ped": "应用", "ui_vehicles": "游戏载具", "ui_spawn": "生成", "ui_spawn_by_name": "直接生成", "ui_no_vehicle_found": "无有效的载具数据", "ui_weapons": "游戏武器", "ui_give_weapon_by_name": "直接获取", "ui_give_weapon": "获取", "ui_no_weapon_found": "无有效的武器数据", "ui_set_coords_as_string": "字符串格式", "ui_set_coords_separate": "分隔值格式", "ui_confirm": "确认", "ui_cancel": "取消", "ui_location_name": "位置坐标名称", "ui_create_location_description": "将保存您当前的坐标和朝向数据", "ui_add_entity": "增加新的实体模型", "ui_add_entity_description": "输入实体模型代码", "ui_delete_all_entities": "删除所有已生成的实体模型?", "ui_amount": "数量", "ui_portal_flag_1": "禁用外部渲染", "ui_portal_flag_2": "禁用内部渲染", "ui_portal_flag_4": "可反射属性", "ui_portal_flag_8": "额外bloom效果", "ui_portal_flag_16": "16 号未知属性", "ui_portal_flag_32": "使用外部 LOD", "ui_portal_flag_64": "当门关闭时隐藏", "ui_portal_flag_128": "128 号未知属性", "ui_portal_flag_256": "镜面外部门户", "ui_portal_flag_512": "512 号未知属性", "ui_portal_flag_1024": "镜像地狱实体", "ui_portal_flag_2048": "2048 号未知属性", "ui_portal_flag_4096": "Unknown 4096 号未知属性", "ui_portal_flag_8192": "禁用远部剪切", "ui_update_warning": "可用更新!", "ui_github": "打开Github存储库", "ui_discord": "加入Dolu的Discord群组", "ui_audio": "世界音效", "ui_static_emitters": "静态发射器", "ui_draw_static_emitters": "显示静态发射器", "ui_draw_distance": "绘制距离", "ui_closest_emitter_info": "最近的发射器信息", "ui_refresh": "刷新", "ui_distance": "距离", "ui_meters": "米", "ui_flags": "Flags属性", "ui_room": "Room属性", "ui_radio_station": "广播电台"}