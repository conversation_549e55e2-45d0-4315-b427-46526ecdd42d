import type { Snowflake } from '../../../../globals';
import type { APIRole, APIUser } from '../../index';
import type { APIApplicationCommandInteractionWrapper, APIInteractionDataResolvedChannel, APIInteractionDataResolvedGuildMember, ApplicationCommandType } from '../applicationCommands';
import type { APIDMInteractionWrapper, APIGuildInteractionWrapper } from '../base';
import type { APIBaseApplicationCommandInteractionData } from './internals';
import type { APIApplicationCommandStringOption, APIApplicationCommandInteractionDataStringOption } from './_chatInput/string';
import type { APIApplicationCommandIntegerOption, APIApplicationCommandInteractionDataIntegerOption } from './_chatInput/integer';
import type { APIApplicationCommandBooleanOption, APIApplicationCommandInteractionDataBooleanOption } from './_chatInput/boolean';
import type { APIApplicationCommandUserOption, APIApplicationCommandInteractionDataUserOption } from './_chatInput/user';
import type { APIApplicationCommandChannelOption, APIApplicationCommandInteractionDataChannelOption } from './_chatInput/channel';
import type { APIApplicationCommandRoleOption, APIApplicationCommandInteractionDataRoleOption } from './_chatInput/role';
import type { APIApplicationCommandMentionableOption, APIApplicationCommandInteractionDataMentionableOption } from './_chatInput/mentionable';
import type { APIApplicationCommandNumberOption, APIApplicationCommandInteractionDataNumberOption } from './_chatInput/number';
import type { APIApplicationCommandSubcommandOption, APIApplicationCommandInteractionDataSubcommandOption } from './_chatInput/subcommand';
import type { APIApplicationCommandSubcommandGroupOption, APIApplicationCommandInteractionDataSubcommandGroupOption } from './_chatInput/subcommandGroup';
export * from './_chatInput/string';
export * from './_chatInput/integer';
export * from './_chatInput/boolean';
export * from './_chatInput/user';
export * from './_chatInput/channel';
export * from './_chatInput/role';
export * from './_chatInput/mentionable';
export * from './_chatInput/number';
export * from './_chatInput/subcommand';
export * from './_chatInput/subcommandGroup';
export * from './_chatInput/shared';
/**
 * https://discord.com/developers/docs/interactions/application-commands#application-command-object-application-command-option-structure
 */
export declare type APIApplicationCommandBasicOption = APIApplicationCommandStringOption | APIApplicationCommandIntegerOption | APIApplicationCommandBooleanOption | APIApplicationCommandUserOption | APIApplicationCommandChannelOption | APIApplicationCommandRoleOption | APIApplicationCommandMentionableOption | APIApplicationCommandNumberOption;
/**
 * https://discord.com/developers/docs/interactions/application-commands#application-command-object-application-command-option-structure
 */
export declare type APIApplicationCommandOption = APIApplicationCommandSubcommandOption | APIApplicationCommandSubcommandGroupOption | APIApplicationCommandBasicOption;
/**
 * https://discord.com/developers/docs/interactions/application-commands#application-command-object-application-command-interaction-data-option-structure
 */
export declare type APIApplicationCommandInteractionDataOption = APIApplicationCommandInteractionDataSubcommandOption | APIApplicationCommandInteractionDataSubcommandGroupOption | APIApplicationCommandInteractionDataBasicOption;
export declare type APIApplicationCommandInteractionDataBasicOption = APIApplicationCommandInteractionDataStringOption | APIApplicationCommandInteractionDataIntegerOption | APIApplicationCommandInteractionDataBooleanOption | APIApplicationCommandInteractionDataUserOption | APIApplicationCommandInteractionDataChannelOption | APIApplicationCommandInteractionDataRoleOption | APIApplicationCommandInteractionDataMentionableOption | APIApplicationCommandInteractionDataNumberOption;
/**
 * https://discord.com/developers/docs/interactions/receiving-and-responding#interaction-object-interaction-data-structure
 */
export interface APIChatInputApplicationCommandInteractionData extends APIBaseApplicationCommandInteractionData<ApplicationCommandType.ChatInput> {
    options?: APIApplicationCommandInteractionDataOption[];
    resolved?: APIChatInputApplicationCommandInteractionDataResolved;
}
/**
 * https://discord.com/developers/docs/interactions/receiving-and-responding#interaction-object-resolved-data-structure
 */
export interface APIChatInputApplicationCommandInteractionDataResolved {
    users?: Record<Snowflake, APIUser>;
    roles?: Record<Snowflake, APIRole>;
    members?: Record<Snowflake, APIInteractionDataResolvedGuildMember>;
    channels?: Record<Snowflake, APIInteractionDataResolvedChannel>;
}
/**
 * https://discord.com/developers/docs/interactions/receiving-and-responding#interaction-object
 */
export declare type APIChatInputApplicationCommandInteraction = APIApplicationCommandInteractionWrapper<APIChatInputApplicationCommandInteractionData>;
/**
 * https://discord.com/developers/docs/interactions/receiving-and-responding#interaction-object
 */
export declare type APIChatInputApplicationCommandDMInteraction = APIDMInteractionWrapper<APIChatInputApplicationCommandInteraction>;
/**
 * https://discord.com/developers/docs/interactions/receiving-and-responding#interaction-object
 */
export declare type APIChatInputApplicationCommandGuildInteraction = APIGuildInteractionWrapper<APIChatInputApplicationCommandInteraction>;
//# sourceMappingURL=chatInput.d.ts.map