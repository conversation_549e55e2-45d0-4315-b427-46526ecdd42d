var G=Object.defineProperty;var ge=Object.getOwnPropertyDescriptor;var Pe=(t,e,i)=>e in t?G(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i;var q=(t,e)=>{for(var i in e)G(t,i,{get:e[i],enumerable:!0})};var m=(t,e,i,o)=>{for(var p=o>1?void 0:o?ge(e,i):e,A=t.length-1,y;A>=0;A--)(y=t[A])&&(p=(o?y(e,i,p):y(p))||p);return o&&p&&G(e,i,p),p};var n=(t,e,i)=>(Pe(t,typeof e!="symbol"?e+"":e,i),i);var le={};q(le,{authorNamePredicate:()=>z,colorPredicate:()=>W,descriptionPredicate:()=>j,embedFieldPredicate:()=>pe,embedFieldsArrayPredicate:()=>w,fieldInlinePredicate:()=>B,fieldLengthPredicate:()=>me,fieldNamePredicate:()=>O,fieldValuePredicate:()=>N,footerTextPredicate:()=>Z,timestampPredicate:()=>K,titlePredicate:()=>Q,urlPredicate:()=>u,validateFieldLength:()=>E});import{z as l}from"zod";var O=l.string().min(1).max(256),N=l.string().min(1).max(1024),B=l.boolean().optional(),pe=l.object({name:O,value:N,inline:B}),w=pe.array(),me=l.number().lte(25);function E(t,e){me.parse(t.length+e)}var z=O.nullable(),u=l.string().url().nullish(),W=l.number().gte(0).lte(16777215).nullable(),j=l.string().min(1).max(4096).nullable(),Z=l.string().min(1).max(2048).nullable(),K=l.union([l.number(),l.date()]).nullable(),Q=O.nullable();var k=class{constructor(e={}){n(this,"fields");n(this,"title");n(this,"description");n(this,"url");n(this,"color");n(this,"timestamp");n(this,"thumbnail");n(this,"image");n(this,"video");n(this,"author");n(this,"provider");n(this,"footer");this.title=e.title,this.description=e.description,this.url=e.url,this.color=e.color,this.thumbnail=e.thumbnail,this.image=e.image,this.video=e.video,this.author=e.author,this.provider=e.provider,this.footer=e.footer,this.fields=e.fields??[],e.timestamp&&(this.timestamp=new Date(e.timestamp).toISOString())}get length(){return(this.title?.length??0)+(this.description?.length??0)+this.fields.reduce((e,i)=>e+i.name.length+i.value.length,0)+(this.footer?.text.length??0)+(this.author?.name.length??0)}addField(e){return this.addFields(e)}addFields(...e){return w.parse(e),E(this.fields,e.length),this.fields.push(...k.normalizeFields(...e)),this}spliceFields(e,i,...o){return w.parse(o),E(this.fields,o.length-i),this.fields.splice(e,i,...k.normalizeFields(...o)),this}setAuthor(e){if(e===null)return this.author=void 0,this;let{name:i,iconURL:o,url:p}=e;return z.parse(i),u.parse(o),u.parse(p),this.author={name:i,url:p,icon_url:o},this}setColor(e){return W.parse(e),this.color=e??void 0,this}setDescription(e){return j.parse(e),this.description=e??void 0,this}setFooter(e){if(e===null)return this.footer=void 0,this;let{text:i,iconURL:o}=e;return Z.parse(i),u.parse(o),this.footer={text:i,icon_url:o},this}setImage(e){return u.parse(e),this.image=e?{url:e}:void 0,this}setThumbnail(e){return u.parse(e),this.thumbnail=e?{url:e}:void 0,this}setTimestamp(e=Date.now()){return K.parse(e),this.timestamp=e?new Date(e).toISOString():void 0,this}setTitle(e){return Q.parse(e),this.title=e??void 0,this}setURL(e){return u.parse(e),this.url=e??void 0,this}toJSON(){return{...this}}static normalizeFields(...e){return e.flat(1/0).map(i=>(O.parse(i.name),N.parse(i.value),B.parse(i.inline),{name:i.name,value:i.value,inline:i.inline??void 0}))}};function tt(t,e){return typeof e=="undefined"?`\`\`\`
${t}\`\`\``:`\`\`\`${t}
${e}\`\`\``}function it(t){return`\`${t}\``}function nt(t){return`_${t}_`}function ot(t){return`**${t}**`}function rt(t){return`__${t}__`}function at(t){return`~~${t}~~`}function st(t){return`> ${t}`}function pt(t){return`>>> ${t}`}function mt(t){return`<${t}>`}function lt(t,e,i){return i?`[${t}](${e} "${i}")`:`[${t}](${e})`}function dt(t){return`||${t}||`}function ct(t){return`<@${t}>`}function ut(t){return`<@!${t}>`}function ht(t){return`<#${t}>`}function Ct(t){return`<@&${t}>`}function ft(t,e=!1){return`<${e?"a":""}:_:${t}>`}function bt(t,e){return typeof t!="number"&&(t=Math.floor((t?.getTime()??Date.now())/1e3)),typeof e=="string"?`<t:${t}:${e}>`:`<t:${t}>`}var xt={ShortTime:"t",LongTime:"T",ShortDate:"d",LongDate:"D",ShortDateTime:"f",LongDateTime:"F",RelativeTime:"R"},Te=(o=>(o.Shrug="\xAF\\_(\u30C4)\\_/\xAF",o.Tableflip="(\u256F\xB0\u25A1\xB0\uFF09\u256F\uFE35 \u253B\u2501\u253B",o.Unflip="\u252C\u2500\u252C \u30CE( \u309C-\u309C\u30CE)",o))(Te||{});var ue={};q(ue,{assertReturnOfBuilder:()=>C,validateDefaultPermission:()=>X,validateDescription:()=>V,validateMaxChoicesLength:()=>Y,validateMaxOptionsLength:()=>d,validateName:()=>L,validateRequired:()=>U,validateRequiredParameters:()=>h});import H from"@sindresorhus/is";import{z as _}from"zod";function h(t,e,i){L(t),V(e),d(i)}var Ie=_.string().min(1).max(32).regex(/^[\P{Lu}\p{N}_-]+$/u);function L(t){Ie.parse(t)}var $e=_.string().min(1).max(100);function V(t){$e.parse(t)}var de=_.boolean();function X(t){de.parse(t)}function U(t){de.parse(t)}var ce=_.unknown().array().max(25);function d(t){ce.parse(t)}function Y(t){ce.parse(t)}function C(t,e){let i=e.name;if(H.nullOrUndefined(t))throw new TypeError(`Expected to receive a ${i} builder, got ${t===null?"null":"undefined"} instead.`);if(H.primitive(t))throw new TypeError(`Expected to receive a ${i} builder, got a primitive (${typeof t}) instead.`);if(!(t instanceof e)){let o=t,p=H.function_(t)?t.name:o.constructor.name,A=Reflect.get(o,Symbol.toStringTag),y=A?`${p} [${A}]`:p;throw new TypeError(`Expected to receive a ${i} builder, got ${y} instead.`)}}import{mix as We}from"ts-mixer";import{ApplicationCommandOptionType as Re}from"discord-api-types/v9";var f=class{constructor(){n(this,"name");n(this,"description")}setName(e){return L(e),Reflect.set(this,"name",e),this}setDescription(e){return V(e),Reflect.set(this,"description",e),this}};var s=class extends f{constructor(){super(...arguments);n(this,"required",!1)}setRequired(e){return U(e),Reflect.set(this,"required",e),this}runRequiredValidations(){h(this.name,this.description,[]),U(this.required)}};var ee=class extends s{constructor(){super(...arguments);n(this,"type",Re.Boolean)}toJSON(){return this.runRequiredValidations(),{...this}}};import{ApplicationCommandOptionType as Ne}from"discord-api-types/v9";import{mix as Be}from"ts-mixer";import{ChannelType as c}from"discord-api-types/v9";import{z as he}from"zod";var Me=[c.GuildText,c.GuildVoice,c.GuildCategory,c.GuildNews,c.GuildStore,c.GuildNewsThread,c.GuildPublicThread,c.GuildPrivateThread,c.GuildStageVoice],ve=he.union(Me.map(t=>he.literal(t))),te=class{constructor(){n(this,"channel_types")}addChannelType(e){return this.channel_types===void 0&&Reflect.set(this,"channel_types",[]),ve.parse(e),this.channel_types.push(e),this}addChannelTypes(e){return e.forEach(i=>this.addChannelType(i)),this}};var g=class extends s{constructor(){super(...arguments);n(this,"type",Ne.Channel)}toJSON(){return this.runRequiredValidations(),{...this}}};g=m([Be(te)],g);import{ApplicationCommandOptionType as ke}from"discord-api-types/v9";import{mix as _e}from"ts-mixer";import{z as Le}from"zod";var P=class{constructor(){n(this,"maxValue");n(this,"minValue")}};import{ApplicationCommandOptionType as we}from"discord-api-types/v9";import{z as T}from"zod";var D=T.string().min(1).max(100),Ce=T.number().gt(-1/0).lt(1/0),fe=T.tuple([D,T.union([D,Ce])]).array(),Ee=T.boolean(),x=class{constructor(){n(this,"choices");n(this,"autocomplete");n(this,"type")}addChoice(e,i){if(this.autocomplete)throw new RangeError("Autocomplete and choices are mutually exclusive to each other.");return this.choices===void 0&&Reflect.set(this,"choices",[]),Y(this.choices),D.parse(e),this.type===we.String?D.parse(i):Ce.parse(i),this.choices.push({name:e,value:i}),this}addChoices(e){if(this.autocomplete)throw new RangeError("Autocomplete and choices are mutually exclusive to each other.");fe.parse(e);for(let[i,o]of e)this.addChoice(i,o);return this}setChoices(e){if(e.length>0&&this.autocomplete)throw new RangeError("Autocomplete and choices are mutually exclusive to each other.");fe.parse(e),Reflect.set(this,"choices",[]);for(let[i,o]of e)this.addChoice(i,o);return this}setAutocomplete(e){if(Ee.parse(e),e&&Array.isArray(this.choices)&&this.choices.length>0)throw new RangeError("Autocomplete and choices are mutually exclusive to each other.");return Reflect.set(this,"autocomplete",e),this}};var be=Le.number().int().nonnegative(),I=class extends s{constructor(){super(...arguments);n(this,"type",ke.Integer)}setMaxValue(e){return be.parse(e),Reflect.set(this,"maxValue",e),this}setMinValue(e){return be.parse(e),Reflect.set(this,"minValue",e),this}toJSON(){if(this.runRequiredValidations(),this.autocomplete&&Array.isArray(this.choices)&&this.choices.length>0)throw new RangeError("Autocomplete and choices are mutually exclusive to each other.");return{...this}}};I=m([_e(P,x)],I);import{ApplicationCommandOptionType as Ve}from"discord-api-types/v9";var ie=class extends s{constructor(){super(...arguments);n(this,"type",Ve.Mentionable)}toJSON(){return this.runRequiredValidations(),{...this}}};import{ApplicationCommandOptionType as Ue}from"discord-api-types/v9";import{mix as De}from"ts-mixer";import{z as Fe}from"zod";var xe=Fe.number().nonnegative(),$=class extends s{constructor(){super(...arguments);n(this,"type",Ue.Number)}setMaxValue(e){return xe.parse(e),Reflect.set(this,"maxValue",e),this}setMinValue(e){return xe.parse(e),Reflect.set(this,"minValue",e),this}toJSON(){if(this.runRequiredValidations(),this.autocomplete&&Array.isArray(this.choices)&&this.choices.length>0)throw new RangeError("Autocomplete and choices are mutually exclusive to each other.");return{...this}}};$=m([De(P,x)],$);import{ApplicationCommandOptionType as Je}from"discord-api-types/v9";var ne=class extends s{constructor(){super(...arguments);n(this,"type",Je.Role)}toJSON(){return this.runRequiredValidations(),{...this}}};import{ApplicationCommandOptionType as Ge}from"discord-api-types/v9";import{mix as qe}from"ts-mixer";var R=class extends s{constructor(){super(...arguments);n(this,"type",Ge.String)}toJSON(){if(this.runRequiredValidations(),this.autocomplete&&Array.isArray(this.choices)&&this.choices.length>0)throw new RangeError("Autocomplete and choices are mutually exclusive to each other.");return{...this}}};R=m([qe(x)],R);import{ApplicationCommandOptionType as ze}from"discord-api-types/v9";var oe=class extends s{constructor(){super(...arguments);n(this,"type",ze.User)}toJSON(){return this.runRequiredValidations(),{...this}}};var M=class{constructor(){n(this,"options")}addBooleanOption(e){return this._sharedAddOptionMethod(e,ee)}addUserOption(e){return this._sharedAddOptionMethod(e,oe)}addChannelOption(e){return this._sharedAddOptionMethod(e,g)}addRoleOption(e){return this._sharedAddOptionMethod(e,ne)}addMentionableOption(e){return this._sharedAddOptionMethod(e,ie)}addStringOption(e){return this._sharedAddOptionMethod(e,R)}addIntegerOption(e){return this._sharedAddOptionMethod(e,I)}addNumberOption(e){return this._sharedAddOptionMethod(e,$)}_sharedAddOptionMethod(e,i){let{options:o}=this;d(o);let p=typeof e=="function"?e(new i):e;return C(p,i),o.push(p),this}};import{ApplicationCommandOptionType as Ae}from"discord-api-types/v9";import{mix as Oe}from"ts-mixer";var S=class{constructor(){n(this,"name");n(this,"description");n(this,"options",[])}addSubcommand(e){let{options:i}=this;d(i);let o=typeof e=="function"?e(new b):e;return C(o,b),i.push(o),this}toJSON(){return h(this.name,this.description,this.options),{type:Ae.SubcommandGroup,name:this.name,description:this.description,options:this.options.map(e=>e.toJSON())}}};S=m([Oe(f)],S);var b=class{constructor(){n(this,"name");n(this,"description");n(this,"options",[])}toJSON(){return h(this.name,this.description,this.options),{type:Ae.Subcommand,name:this.name,description:this.description,options:this.options.map(e=>e.toJSON())}}};b=m([Oe(f,M)],b);var re=class{constructor(){n(this,"name");n(this,"description");n(this,"options",[]);n(this,"defaultPermission")}toJSON(){return h(this.name,this.description,this.options),{name:this.name,description:this.description,options:this.options.map(e=>e.toJSON()),default_permission:this.defaultPermission}}setDefaultPermission(e){return X(e),Reflect.set(this,"defaultPermission",e),this}addSubcommandGroup(e){let{options:i}=this;d(i);let o=typeof e=="function"?e(new S):e;return C(o,S),i.push(o),this}addSubcommand(e){let{options:i}=this;d(i);let o=typeof e=="function"?e(new b):e;return C(o,b),i.push(o),this}};re=m([We(M,f)],re);var ye={};q(ye,{validateDefaultPermission:()=>se,validateName:()=>F,validateRequiredParameters:()=>ae,validateType:()=>J});import{z as v}from"zod";import{ApplicationCommandType as Se}from"discord-api-types/v9";function ae(t,e){F(t),J(e)}var je=v.string().min(1).max(32).regex(/^( *[\p{L}\p{N}_-]+ *)+$/u);function F(t){je.parse(t)}var Ze=v.union([v.literal(Se.User),v.literal(Se.Message)]);function J(t){Ze.parse(t)}var Ke=v.boolean();function se(t){Ke.parse(t)}var Qe=class{constructor(){n(this,"name");n(this,"type");n(this,"defaultPermission")}setName(e){return F(e),Reflect.set(this,"name",e),this}setType(e){return J(e),Reflect.set(this,"type",e),this}setDefaultPermission(e){return se(e),Reflect.set(this,"defaultPermission",e),this}toJSON(){return ae(this.name,this.type),{name:this.name,type:this.type,default_permission:this.defaultPermission}}};export{ye as ContextMenuCommandAssertions,Qe as ContextMenuCommandBuilder,k as Embed,le as EmbedAssertions,Te as Faces,ue as SlashCommandAssertions,ee as SlashCommandBooleanOption,re as SlashCommandBuilder,g as SlashCommandChannelOption,I as SlashCommandIntegerOption,ie as SlashCommandMentionableOption,$ as SlashCommandNumberOption,ne as SlashCommandRoleOption,R as SlashCommandStringOption,b as SlashCommandSubcommandBuilder,S as SlashCommandSubcommandGroupBuilder,oe as SlashCommandUserOption,xt as TimestampStyles,pt as blockQuote,ot as bold,ht as channelMention,tt as codeBlock,ft as formatEmoji,mt as hideLinkEmbed,lt as hyperlink,it as inlineCode,nt as italic,ut as memberNicknameMention,st as quote,Ct as roleMention,dt as spoiler,at as strikethrough,bt as time,rt as underscore,ct as userMention};
//# sourceMappingURL=index.mjs.map