{"cannot_goback": "Ultima posizione non trovata. Devi teletrasportarti prima", "no_marker": "Non hai impostato alcun Marker", "command_tpm": "%s Teleportati al marker", "command_noclip": "%s <PERSON>lto noclip", "command_openui": "%s <PERSON>i <PERSON>", "command_weather_notfound": "Cercando di impostare il meteo sbagliato: %s", "teleport_invalid_coords": "Cercando di teletrasportare il giocatore a un tipo di coordinate non valido", "model_doesnt_exist": "Model %s non esiste", "copied_coords_clipboard": "Coordinate copiate negli appunti", "copied_model_clipboard": "Hash del modello copiato negli appunti", "press_escape_exit": "Premi 'Escape' per uscire dalla modalità di modifica", "custom_location_created": "Posizione personalizzata creata con successo", "vehicle_upgraded": "Veicolo migliorato con successo!", "weapon_gave": "Hai appena ricevuto un'arma", "weapon_cant_carry": "Non puoi ricevere quest'arma", "max_health_set": "Salute massima impostata con successo", "entity_cant_be_loaded": "L'entita' non può essere caricata...", "entity_doesnt_exist": "Entità inesistente", "entity_deleted": "Entità eliminata con successo", "teleport_success": "Teletrasportato con successo. Usa /goback per tornare all'ultima posizione", "ui_home": "Casa", "ui_world": "<PERSON><PERSON>", "ui_exit": "<PERSON><PERSON><PERSON>", "ui_copy_coords": "Copia Coordinate", "ui_copied_coords": "Coordinate Copiate", "ui_copy_name": "Copia nome", "ui_copied_name": "Nome copiato", "ui_copy_hash": "Copia hash", "ui_copied_hash": "Hash copiato", "ui_name": "Nome", "ui_hash": "Hash", "ui_coords": "Coordinate", "ui_heading": "Heading", "ui_interior_id": "Interior ID", "ui_current_room": "<PERSON><PERSON> corrente", "ui_teleport": "Teletrasportati", "ui_not_in_interior": "Non sei all'interno di un interno", "ui_no_last_location": "Non ti sei ancora teletrasportato in nessuna posizione", "ui_current_coords": "Coordinate attuali", "ui_set_coords": "Imposta coordinate", "ui_save_location": "Salva come posizione", "ui_last_location": "Ultima posizione", "ui_current_interior": "Interno attuale", "ui_quick_actions": "Azioni rapide", "ui_clean_zone": "Pulisci zona", "ui_clean_ped": "<PERSON><PERSON><PERSON><PERSON> ped", "ui_upgrade_vehicle": "Potenzia veicolo", "ui_repair_vehicle": "<PERSON><PERSON><PERSON>lo", "ui_delete_vehicle": "Elimina veicolo", "ui_set_sunny_day": "Imposta giornata soleggiata", "ui_spawn_vehicle": "Genera veicolo", "ui_max_health": "Salute massima", "ui_time_freeze": "Tempo congelato", "ui_time_not_freeze": "Tempo non congelato", "ui_time": "Tempo", "ui_sync": "Sincronizza", "ui_freeze_time": "Congela tempo", "ui_weather": "Meteo", "ui_choose_weather": "Scegli un tipo di meteo", "ui_current_weather": "Meteo attuale?", "ui_freeze_weather": "<PERSON><PERSON><PERSON> meteo", "ui_interior": "Interno", "ui_room_count": "Numero stanze", "ui_portal_count": "Numero portali", "ui_portals": "<PERSON><PERSON>", "ui_infos": "Informazioni", "ui_fill_portals": "Riempi", "ui_outline_portals": "Contorno", "ui_corcers_portals": "Ang<PERSON>", "ui_flag": "Flag", "ui_room_from": "<PERSON><PERSON>", "ui_room_to": "Stanza a", "ui_index": "Indice", "ui_timecycle": "<PERSON><PERSON><PERSON> del tempo", "ui_no_timecycle_found": "<PERSON><PERSON><PERSON> ciclo del tempo trovato", "ui_object_spawner": "Generatore di oggetti", "ui_locations": "Posizioni", "ui_snap_to_ground": "Allinea al suolo", "ui_duplicate": "Duplica", "ui_no_location_found": "Nessuna posizione trovata", "ui_goto": "Vai a", "ui_show_custom_locations": "Mostra posizioni personalizzate", "ui_show_vanilla_locations": "Mostra posizioni base", "ui_create_custom_location": "Crea posizione personalizzata", "ui_search": "Cerca", "ui_rename": "Rinomina", "ui_delete": "Elimina", "ui_vanilla": "Base", "ui_custom": "<PERSON><PERSON><PERSON><PERSON>", "ui_peds": "<PERSON><PERSON><PERSON>", "ui_no_ped_found": "<PERSON><PERSON><PERSON> pedone trovato", "ui_set_by_name": "Imposta per nome", "ui_set_ped": "Applica", "ui_vehicles": "<PERSON><PERSON><PERSON><PERSON>", "ui_spawn": "Genera", "ui_spawn_by_name": "Genera per nome", "ui_no_vehicle_found": "<PERSON><PERSON><PERSON>lo trovato", "ui_weapons": "Arm<PERSON>", "ui_give_weapon_by_name": "Dai arma per nome", "ui_give_weapon": "Dai", "ui_no_weapon_found": "Nessuna arma trovata", "ui_set_coords_as_string": "Imposta come stringa", "ui_set_coords_separate": "Imposta come valori separati", "ui_confirm": "Conferma", "ui_cancel": "<PERSON><PERSON><PERSON>", "ui_location_name": "Nome posizione", "ui_create_location_description": "Salverà le tue coordinate e direzione attuali", "ui_add_entity": "Aggiungi una nuova entità", "ui_add_entity_description": "Inserisci il nome dell'entità", "ui_delete_all_entities": "Eliminare tutte le entità generate?", "ui_amount": "Quantità", "ui_portal_flag_1": "Disattiva rendering esterno", "ui_portal_flag_2": "Disattiva rendering interno", "ui_portal_flag_4": "<PERSON><PERSON><PERSON><PERSON>", "ui_portal_flag_8": "Luminosità extra", "ui_portal_flag_16": "Sconosciuto 16", "ui_portal_flag_32": "Usa LOD esterno", "ui_portal_flag_64": "Nascondi quando la porta è chiusa", "ui_portal_flag_128": "Sconosciuto 128", "ui_portal_flag_256": "Specchia portali esterni", "ui_portal_flag_512": "Sconosciuto 512", "ui_portal_flag_1024": "Specchia entità del limbo", "ui_portal_flag_2048": "Sconosciuto 2048", "ui_portal_flag_4096": "Sconosciuto 4096", "ui_portal_flag_8192": "Disattiva farclipping", "ui_update_warning": "Aggiornamento disponibile!", "ui_github": "Apri repository Github", "ui_discord": "Unisciti a Dolu Discord", "ui_audio": "Audio", "ui_static_emitters": "Emettitori statici", "ui_draw_static_emitters": "Mostra emettitori statici", "ui_draw_distance": "Distanza di visualizzazione", "ui_closest_emitter_info": "Info emettitore più vicino", "ui_refresh": "Aggiorna", "ui_distance": "Distanza", "ui_meters": "metri", "ui_flags": "Flags", "ui_room": "<PERSON><PERSON>", "ui_radio_station": "Stazione radio", "ui_copied_rotation": "Rotazione copiata", "ui_copy_rotation": "Copia rotazione"}