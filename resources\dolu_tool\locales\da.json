{"cannot_goback": "Ingen sidste placering fundet. Du skal teleportere et sted hen først", "no_marker": "Du har ikke angivet nogen markør", "command_tpm": "%s Teleport til markør", "command_noclip": "%s Skift noclip", "command_openui": "%s Åbn Dolu-værktøj", "command_weather_notfound": "Prøver at indstille en ugyldig vejrtype: %s", "teleport_invalid_coords": "Prøver at teleportere spiller til ugyldig koordinatstype", "model_doesnt_exist": "Model %s findes ikke", "copied_coords_clipboard": "Koordinater kopieret til udklipsholder", "copied_model_clipboard": "Model-hash kopieret til udklipsholder", "press_escape_exit": "Tryk på 'Escape' for at afslutte redigeringsfunktionen", "custom_location_created": "Brugerdefineret placering oprettet!", "vehicle_upgraded": "Køretøj opgraderet!", "weapon_gave": "<PERSON> modtog et våben", "weapon_cant_carry": "Du kan ikke modtage dette våben", "max_health_set": "Du blev helbredt", "entity_cant_be_loaded": "Objektet kan ikke indlæses...", "entity_doesnt_exist": "Objectet findes ikke", "entity_deleted": "Objectet slettet!", "teleport_success": "Du blev teleporteret. Brug /goback for at vende tilbage til sidste placering", "ui_home": "<PERSON><PERSON><PERSON>", "ui_world": "Verden", "ui_exit": "A<PERSON>lut", "ui_copy_coords": "<PERSON><PERSON><PERSON> coords", "ui_copied_coords": "<PERSON><PERSON><PERSON><PERSON> coords", "ui_copy_name": "<PERSON><PERSON>r navn", "ui_copied_name": "Kopierede navn", "ui_copy_hash": "<PERSON><PERSON><PERSON> hash", "ui_copied_hash": "<PERSON><PERSON><PERSON><PERSON> hash", "ui_name": "Navn", "ui_hash": "Hash", "ui_coords": "<PERSON><PERSON><PERSON>", "ui_heading": "Heading", "ui_interior_id": "Interiør ID", "ui_current_room": "Aktuelt rum", "ui_teleport": "Teleport", "ui_not_in_interior": "Du er ikke inde i nogen interiør", "ui_no_last_location": "Du har endnu ikke teleporteret til nogen placering", "ui_current_coords": "Aktuel placering", "ui_set_coords": "G<PERSON> til coords ", "ui_save_location": "Gem placering", "ui_last_location": "Sidste placering", "ui_current_interior": "Aktuelt interiør", "ui_quick_actions": "Hurtige handlinger", "ui_clean_zone": "Ryd zone", "ui_clean_ped": "<PERSON><PERSON> ped", "ui_upgrade_vehicle": "<PERSON><PERSON>", "ui_repair_vehicle": "<PERSON><PERSON><PERSON>", "ui_delete_vehicle": "Slet køretøj", "ui_set_sunny_day": "<PERSON><PERSON>t solrig dag", "ui_spawn_vehicle": "Spawn køretøj", "ui_max_health": "Genopliv", "ui_time_freeze": "<PERSON>id frossen", "ui_time_not_freeze": "Tid ikke frossen", "ui_time": "Tid", "ui_sync": "Sync", "ui_freeze_time": "<PERSON><PERSON> tid", "ui_weather": "<PERSON><PERSON><PERSON>", "ui_choose_weather": "Vælg en vejrtype", "ui_current_weather": "Aktuelt vejr?", "ui_freeze_weather": "<PERSON><PERSON> vejr", "ui_interior": "Interiør", "ui_room_count": "Antal rum", "ui_portal_count": "Portalantal", "ui_portals": "<PERSON><PERSON>", "ui_infos": "Oplysninger", "ui_fill_portals": "Udfyld", "ui_outline_portals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ui_corcers_portals": "<PERSON><PERSON><PERSON><PERSON>", "ui_flag": "Flag", "ui_room_from": "Rum fra", "ui_room_to": "Rum til", "ui_index": "<PERSON><PERSON><PERSON>", "ui_timecycle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ui_no_timecycle_found": "Ingen tidscyklus fundet", "ui_object_spawner": "Objekt spawner", "ui_locations": "<PERSON><PERSON><PERSON>", "ui_snap_to_ground": "Snap til jorden", "ui_duplicate": "<PERSON><PERSON><PERSON><PERSON>", "ui_no_location_found": "Ingen placering fundet", "ui_goto": "<PERSON><PERSON> <PERSON>", "ui_show_custom_locations": "Vis brugerde<PERSON><PERSON>e placeringer", "ui_show_vanilla_locations": "<PERSON>is standarte placeringer", "ui_create_custom_location": "Opret brugerdefineret placering", "ui_search": "<PERSON><PERSON><PERSON>", "ui_rename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ui_delete": "Slet", "ui_vanilla": "<PERSON><PERSON>", "ui_custom": "Brugerdefineret", "ui_peds": "Peds", "ui_no_ped_found": "Ingen ped fundet", "ui_set_by_name": "Indstil efter navn", "ui_set_ped": "<PERSON><PERSON><PERSON>", "ui_vehicles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ui_spawn": "Spawn", "ui_spawn_by_name": "Spawn efter navn", "ui_no_vehicle_found": "Ingen køretøj fundet", "ui_weapons": "Våben", "ui_give_weapon_by_name": "Giv våben efter navn", "ui_give_weapon": "Giv", "ui_no_weapon_found": "Ingen våben fundet", "ui_set_coords_as_string": "Indsæt samlet værdi", "ui_set_coords_separate": "Indstil som separate værdier", "ui_confirm": "Bekræft", "ui_cancel": "<PERSON><PERSON><PERSON>", "ui_location_name": "Placeringsnavn", "ui_create_location_description": "Gemmer din nuværende coords og heading", "ui_add_entity": "Tilføj et nyt objekt", "ui_add_entity_description": "Indtast navnet på objektet", "ui_delete_all_entities": "Slet alle spawned objekter?", "ui_amount": "<PERSON><PERSON><PERSON>", "ui_portal_flag_1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ui_portal_flag_2": "<PERSON><PERSON><PERSON><PERSON> interiør rendering", "ui_portal_flag_4": "Spejl", "ui_portal_flag_8": "Ekstra bloom", "ui_portal_flag_16": "Ukendt 16", "ui_portal_flag_32": "Brug eksteriør LOD", "ui_portal_flag_64": "Skju<PERSON> når døren er lukket", "ui_portal_flag_128": "<PERSON><PERSON><PERSON> 128", "ui_portal_flag_256": "Spejl eksteriørportaler", "ui_portal_flag_512": "Ukendt 512", "ui_portal_flag_1024": "Spejl limbo entityes", "ui_portal_flag_2048": "Ukendt 2048", "ui_portal_flag_4096": "Ukendt 4096", "ui_portal_flag_8192": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>", "ui_update_warning": "Opdatering tilgængelig!", "ui_github": "Åbn Github Repository", "ui_discord": "<PERSON><PERSON> i <PERSON>lu's Discord", "ui_audio": "Lyd", "ui_static_emitters": "Statisk emitters", "ui_draw_static_emitters": "Vis statiske emitters", "ui_draw_distance": "Tegn afstand", "ui_closest_emitter_info": "Nærmeste Emitter Info", "ui_refresh": "Opdater", "ui_distance": "Afstand", "ui_meters": "meter", "ui_flags": "Flag", "ui_room": "Rum", "ui_radio_station": "Radiostation", "ui_copied_rotation": "<PERSON><PERSON><PERSON> rotation", "ui_copy_rotation": "Kopier rotation"}