# Features:

• Whitelist only mode (You have to be whitelisted, no password prompt)

• Password only mode (You have to know the password, no whitelist check)

• Whitelist and password mode (You have to be whitelisted and know the password)

• Clever mode (If you're whitelisted, you're not asked for a password. If you're not whitelisted, you're asked for one.)

• Easy to configure (password, attempts, Discord link shown on refused connection)

• Shows user all of his/her identifiers and a link upon refused connection to easily copy/paste identifiers.



# Installation:

• Clone or download via github.

• Put it into `resources`.

• Add `start pun_whitelist` to your server.cfg file.

• Start the server. If it's already up just type `refresh` followed by `start pun_whitelist` into the server or client console.



# Credits

• [enferList](https://forum.cfx.re/t/release-enferlist-another-whitelist-script/81697) | Some bits of code but mostly the logic and idea on how I wanted this script to work in the end.

• [Frazzle](https://gist.github.com/FrazzIe/f59813c137496cd94657e6de909775aa) | Basically 100% of the code that makes the passwords work.
