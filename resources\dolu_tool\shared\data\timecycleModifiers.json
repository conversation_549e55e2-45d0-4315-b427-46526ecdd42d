[{"Name": "li", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "underwater", "DlcName": "basegame", "ModificationsCount": 115}, {"Name": "underwater_deep", "DlcName": "basegame", "ModificationsCount": 81}, {"Name": "NoAmbientmult", "DlcName": "basegame", "ModificationsCount": 4}, {"Name": "superDARK", "DlcName": "basegame", "ModificationsCount": 3}, {"Name": "CAMERA_BW", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "Forest", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "micheal", "DlcName": "basegame", "ModificationsCount": 10}, {"Name": "TREVOR", "DlcName": "basegame", "ModificationsCount": 15}, {"Name": "FRANKLIN", "DlcName": "basegame", "ModificationsCount": 20}, {"Name": "Tunnel", "DlcName": "basegame", "ModificationsCount": 13}, {"Name": "carpark", "DlcName": "basegame", "ModificationsCount": 18}, {"Name": "NEW_abattoir", "DlcName": "basegame", "ModificationsCount": 67}, {"Name": "Vagos", "DlcName": "basegame", "ModificationsCount": 23}, {"Name": "cops", "DlcName": "basegame", "ModificationsCount": 18}, {"Name": "Bikers", "DlcName": "basegame", "ModificationsCount": 28}, {"Name": "BikersSPLASH", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "VagosSPLASH", "DlcName": "basegame", "ModificationsCount": 21}, {"Name": "CopsSPLASH", "DlcName": "basegame", "ModificationsCount": 22}, {"Name": "VAGOS_new_garage", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "VAGOS_new_hangout", "DlcName": "basegame", "ModificationsCount": 26}, {"Name": "NEW_jewel", "DlcName": "basegame", "ModificationsCount": 35}, {"Name": "frankilnsAUNTS_new", "DlcName": "basegame", "ModificationsCount": 37}, {"Name": "frankilnsAUNTS_SUNdir", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "StreetLighting", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "NEW_tunnels", "DlcName": "basegame", "ModificationsCount": 27}, {"Name": "NEW_yellowtunnels", "DlcName": "basegame", "ModificationsCount": 55}, {"Name": "NEW_tunnels_hole", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "NEW_tunnels_ditch", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "Paleto", "DlcName": "basegame", "ModificationsCount": 6}, {"Name": "new_bank", "DlcName": "basegame", "ModificationsCount": 17}, {"Name": "ReduceDrawDistance", "DlcName": "basegame", "ModificationsCount": 10}, {"Name": "ReduceDrawDistanceMission", "DlcName": "basegame", "ModificationsCount": 6}, {"Name": "lightpolution", "DlcName": "basegame", "ModificationsCount": 16}, {"Name": "NEW_lesters", "DlcName": "basegame", "ModificationsCount": 35}, {"Name": "ReduceDrawDistanceMAP", "DlcName": "basegame", "ModificationsCount": 6}, {"Name": "reducewaterREF", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "garage", "DlcName": "basegame", "ModificationsCount": 12}, {"Name": "LightPollutionHills", "DlcName": "basegame", "ModificationsCount": 16}, {"Name": "NewMicheal", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "NewMichealupstairs", "DlcName": "basegame", "ModificationsCount": 20}, {"Name": "NewMichealstoilet", "DlcName": "basegame", "ModificationsCount": 18}, {"Name": "NewMichealgirly", "DlcName": "basegame", "ModificationsCount": 29}, {"Name": "WATER_port", "DlcName": "basegame", "ModificationsCount": 60}, {"Name": "WATER_salton", "DlcName": "basegame", "ModificationsCount": 68}, {"Name": "WATER_river", "DlcName": "basegame", "ModificationsCount": 61}, {"Name": "FIB_interview", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "NEW_station_unfinished", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "cashdepot", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "cashdepotEMERGENCY", "DlcName": "basegame", "ModificationsCount": 36}, {"Name": "FrankilinsHOUSEhills", "DlcName": "basegame", "ModificationsCount": 29}, {"Name": "HicksbarNEW", "DlcName": "basegame", "ModificationsCount": 32}, {"Name": "NOdirectLight", "DlcName": "basegame", "ModificationsCount": 0}, {"Name": "SALTONSEA", "DlcName": "basegame", "ModificationsCount": 59}, {"Name": "TUNNEL_green", "DlcName": "basegame", "ModificationsCount": 32}, {"Name": "NewMicheal_night", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "WATER_muddy", "DlcName": "basegame", "ModificationsCount": 69}, {"Name": "WATER_shore", "DlcName": "basegame", "ModificationsCount": 22}, {"Name": "damage", "DlcName": "basegame", "ModificationsCount": 11}, {"Name": "hitped", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "dying", "DlcName": "basegame", "ModificationsCount": 8}, {"Name": "overwater", "DlcName": "basegame", "ModificationsCount": 6}, {"Name": "whitenightlighting", "DlcName": "basegame", "ModificationsCount": 6}, {"Name": "TUNNEL_yellow", "DlcName": "basegame", "ModificationsCount": 32}, {"Name": "buildingTOP", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "WATER_lab", "DlcName": "basegame", "ModificationsCount": 89}, {"Name": "cinema", "DlcName": "basegame", "ModificationsCount": 36}, {"Name": "fireDEPT", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "ranch", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "TUNNEL_white", "DlcName": "basegame", "ModificationsCount": 36}, {"Name": "V_recycle_mainroom", "DlcName": "basegame", "ModificationsCount": 26}, {"Name": "V_recycle_dark", "DlcName": "basegame", "ModificationsCount": 27}, {"Name": "V_recycle_light", "DlcName": "basegame", "ModificationsCount": 26}, {"Name": "lightning_weak", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "lightning_strong", "DlcName": "basegame", "ModificationsCount": 36}, {"Name": "lightning_cloud", "DlcName": "basegame", "ModificationsCount": 9}, {"Name": "gunclubrange", "DlcName": "basegame", "ModificationsCount": 36}, {"Name": "NoAmbientmult_interior", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "FullAmbientmult_interior", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "StreetLightingJunction", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "StreetLightingtraffic", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "Multipayer_spectatorCam", "DlcName": "basegame", "ModificationsCount": 18}, {"Name": "INT_NoAmbientmult", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "INT_NoAmbientmult_art", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "INT_FullAmbientmult", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "INT_FULLAmbientmult_art", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "INT_FULLAmbientmult_both", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "INT_NoAmbientmult_both", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "<PERSON><PERSON><PERSON>", "DlcName": "basegame", "ModificationsCount": 17}, {"Name": "ReduceSSAO", "DlcName": "basegame", "ModificationsCount": 0}, {"Name": "scope_zoom_in", "DlcName": "basegame", "ModificationsCount": 5}, {"Name": "scope_zoom_out", "DlcName": "basegame", "ModificationsCount": 5}, {"Name": "crane_cam", "DlcName": "basegame", "ModificationsCount": 19}, {"Name": "WATER_silty", "DlcName": "basegame", "ModificationsCount": 64}, {"Name": "Trevors_room", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "Hint_cam", "DlcName": "basegame", "ModificationsCount": 16}, {"Name": "venice_canal_tunnel", "DlcName": "basegame", "ModificationsCount": 47}, {"Name": "blackNwhite", "DlcName": "basegame", "ModificationsCount": 22}, {"Name": "projector", "DlcName": "basegame", "ModificationsCount": 12}, {"Name": "paleto_opt", "DlcName": "basegame", "ModificationsCount": 8}, {"Name": "warehouse", "DlcName": "basegame", "ModificationsCount": 27}, {"Name": "pulse", "DlcName": "basegame", "ModificationsCount": 20}, {"Name": "sleeping", "DlcName": "basegame", "ModificationsCount": 10}, {"Name": "INT_garage", "DlcName": "basegame", "ModificationsCount": 19}, {"Name": "nextgen", "DlcName": "basegame", "ModificationsCount": 5}, {"Name": "crane_cam_cinematic", "DlcName": "basegame", "ModificationsCount": 7}, {"Name": "TUNNEL_orange", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "traffic_skycam", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "powerstation", "DlcName": "basegame", "ModificationsCount": 7}, {"Name": "SAWMILL", "DlcName": "basegame", "ModificationsCount": 9}, {"Name": "LODmult_global_reduce", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "LODmult_HD_orphan_reduce", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "LODmult_HD_orphan_LOD_reduce", "DlcName": "basegame", "ModificationsCount": 3}, {"Name": "LODmult_LOD_reduce", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "LODmult_SLOD1_reduce", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "LODmult_SLOD2_reduce", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "LODmult_SLOD3_reduce", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "NewMicheal_upstairs", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "micheals_lightsOFF", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "telescope", "DlcName": "basegame", "ModificationsCount": 22}, {"Name": "WATER_silverlake", "DlcName": "basegame", "ModificationsCount": 56}, {"Name": "WATER _lab_cooling", "DlcName": "basegame", "ModificationsCount": 95}, {"Name": "baseTONEMAPPING", "DlcName": "basegame", "ModificationsCount": 16}, {"Name": "WATER_salton_bottom", "DlcName": "basegame", "ModificationsCount": 37}, {"Name": "new_stripper_changing", "DlcName": "basegame", "ModificationsCount": 29}, {"Name": "underwater_deep_clear", "DlcName": "basegame", "ModificationsCount": 4}, {"Name": "prologue_ending_fog", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "graveyard_shootout", "DlcName": "basegame", "ModificationsCount": 16}, {"Name": "morebloom", "DlcName": "basegame", "ModificationsCount": 3}, {"Name": "LIGHTSreduceFALLOFF", "DlcName": "basegame", "ModificationsCount": 0}, {"Name": "INT_posh_hairdresser", "DlcName": "basegame", "ModificationsCount": 23}, {"Name": "V_strip_office", "DlcName": "basegame", "ModificationsCount": 28}, {"Name": "sunglasses", "DlcName": "basegame", "ModificationsCount": 15}, {"Name": "vespucci_garage", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "half_direct", "DlcName": "basegame", "ModificationsCount": 3}, {"Name": "carpark_dt1_03", "DlcName": "basegame", "ModificationsCount": 20}, {"Name": "tunnel_id1_11", "DlcName": "basegame", "ModificationsCount": 20}, {"Name": "reducelightingcost", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "NOrain", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "morgue_dark", "DlcName": "basegame", "ModificationsCount": 44}, {"Name": "CS3_rail_tunnel", "DlcName": "basegame", "ModificationsCount": 19}, {"Name": "new_tunnels_entrance", "DlcName": "basegame", "ModificationsCount": 47}, {"Name": "spectator1", "DlcName": "basegame", "ModificationsCount": 32}, {"Name": "spectator2", "DlcName": "basegame", "ModificationsCount": 63}, {"Name": "spectator3", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "spectator4", "DlcName": "basegame", "ModificationsCount": 36}, {"Name": "spectator5", "DlcName": "basegame", "ModificationsCount": 43}, {"Name": "spectator6", "DlcName": "basegame", "ModificationsCount": 37}, {"Name": "spectator7", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "spectator8", "DlcName": "basegame", "ModificationsCount": 48}, {"Name": "spectator9", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "spectator10", "DlcName": "basegame", "ModificationsCount": 53}, {"Name": "INT_NOdirectLight", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "WATER_resevoir", "DlcName": "basegame", "ModificationsCount": 68}, {"Name": "WATER_hills", "DlcName": "basegame", "ModificationsCount": 66}, {"Name": "WATER_militaryPOOP", "DlcName": "basegame", "ModificationsCount": 62}, {"Name": "NEW_ornate_bank", "DlcName": "basegame", "ModificationsCount": 43}, {"Name": "NEW_ornate_bank_safe", "DlcName": "basegame", "ModificationsCount": 46}, {"Name": "NEW_ornate_bank_entrance", "DlcName": "basegame", "ModificationsCount": 38}, {"Name": "NEW_ornate_bank_office", "DlcName": "basegame", "ModificationsCount": 46}, {"Name": "LODmult_global_reduce_NOHD", "DlcName": "basegame", "ModificationsCount": 6}, {"Name": "interior_WATER_lighting", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "gorge_reflectionoffset", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "eyeINtheSKY", "DlcName": "basegame", "ModificationsCount": 22}, {"Name": "resvoire_reflection", "DlcName": "basegame", "ModificationsCount": 6}, {"Name": "NO_weather", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "prologue_ext_art_amb", "DlcName": "basegame", "ModificationsCount": 12}, {"Name": "prologue_shootout", "DlcName": "basegame", "ModificationsCount": 20}, {"Name": "heathaze", "DlcName": "basegame", "ModificationsCount": 22}, {"Name": "KT_underpass", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "INT_nowaterREF", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "carMOD_underpass", "DlcName": "basegame", "ModificationsCount": 28}, {"Name": "refit", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "NO_streetAmbient", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "NO_coronas", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "epsilion", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "WATER_refmap_high", "DlcName": "basegame", "ModificationsCount": 0}, {"Name": "WATER_refmap_med", "DlcName": "basegame", "ModificationsCount": 0}, {"Name": "WATER_refmap_low", "DlcName": "basegame", "ModificationsCount": 0}, {"Name": "WATER_refmap_verylow", "DlcName": "basegame", "ModificationsCount": 0}, {"Name": "WATER_refmap_poolside", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "WATER_refmap_silverlake", "DlcName": "basegame", "ModificationsCount": 0}, {"Name": "WATER_refmap_venice", "DlcName": "basegame", "ModificationsCount": 0}, {"Name": "FORdoron_delete", "DlcName": "basegame", "ModificationsCount": 4}, {"Name": "NO_fog_alpha", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "V_strip_nofog", "DlcName": "basegame", "ModificationsCount": 28}, {"Name": "METRO_Tunnels", "DlcName": "basegame", "ModificationsCount": 59}, {"Name": "METRO_Tunnels_entrance", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "METRO_platform", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "STRIP_stage", "DlcName": "basegame", "ModificationsCount": 55}, {"Name": "STRIP_office", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "STRIP_changing", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "INT_NO_fogALPHA", "DlcName": "basegame", "ModificationsCount": 3}, {"Name": "STRIP_nofog", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "INT_streetlighting", "DlcName": "basegame", "ModificationsCount": 8}, {"Name": "ch2_tunnel_whitelight", "DlcName": "basegame", "ModificationsCount": 6}, {"Name": "AmbientPUSH", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "ship_lighting", "DlcName": "basegame", "ModificationsCount": 4}, {"Name": "powerplant_nightlight", "DlcName": "basegame", "ModificationsCount": 8}, {"Name": "paleto_nightlight", "DlcName": "basegame", "ModificationsCount": 8}, {"Name": "militarybase_nightlight", "DlcName": "basegame", "ModificationsCount": 8}, {"Name": "sandyshore_nightlight", "DlcName": "basegame", "ModificationsCount": 8}, {"Name": "jewel_gas", "DlcName": "basegame", "ModificationsCount": 0}, {"Name": "WATER_refmap_off", "DlcName": "basegame", "ModificationsCount": 13}, {"Name": "trailer_explosion_optimise", "DlcName": "basegame", "ModificationsCount": 19}, {"Name": "nervousRON_fog", "DlcName": "basegame", "ModificationsCount": 7}, {"Name": "DONT_overide_sunpos", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "gallery_refmod", "DlcName": "basegame", "ModificationsCount": 20}, {"Name": "prison_nightlight", "DlcName": "basegame", "ModificationsCount": 8}, {"Name": "multiplayer_ped_fight", "DlcName": "basegame", "ModificationsCount": 4}, {"Name": "ship_explosion_underwater", "DlcName": "basegame", "ModificationsCount": 20}, {"Name": "EXTRA_bouncelight", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "secret_camera", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "canyon_mission", "DlcName": "basegame", "ModificationsCount": 23}, {"Name": "gorge_reflection_gpu", "DlcName": "basegame", "ModificationsCount": 4}, {"Name": "subBASE_water_ref", "DlcName": "basegame", "ModificationsCount": 13}, {"Name": "poolsidewaterreflection2", "DlcName": "basegame", "ModificationsCount": 13}, {"Name": "CUSTOM_streetlight", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "ufo", "DlcName": "basegame", "ModificationsCount": 32}, {"Name": "ship_explosion_underwater", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "lab_none_exit", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "FinaleBankexit", "DlcName": "basegame", "ModificationsCount": 28}, {"Name": "prologue_reflection_opt", "DlcName": "basegame", "ModificationsCount": 4}, {"Name": "tunnel_entrance", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "tunnel_entrance_INT", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "id1_11_tunnel", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "reflection_correct_ambient", "DlcName": "basegame", "ModificationsCount": 3}, {"Name": "scanline_cam_cheap", "DlcName": "basegame", "ModificationsCount": 12}, {"Name": "scanline_cam", "DlcName": "basegame", "ModificationsCount": 16}, {"Name": "VC_tunnel_entrance", "DlcName": "basegame", "ModificationsCount": 0}, {"Name": "WATER_REF_malibu", "DlcName": "basegame", "ModificationsCount": 13}, {"Name": "carpark_dt1_02", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "FIB_interview_optimise", "DlcName": "basegame", "ModificationsCount": 21}, {"Name": "Prologue_shootout_opt", "DlcName": "basegame", "ModificationsCount": 10}, {"Name": "hangar_lightsmod", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "plane_inside_mode", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "eatra_bouncelight_beach", "DlcName": "basegame", "ModificationsCount": 5}, {"Name": "downtown_FIB_cascades_opt", "DlcName": "basegame", "ModificationsCount": 3}, {"Name": "jewel_optim", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "gorge_reflectionoffset2", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "ufo_deathray", "DlcName": "basegame", "ModificationsCount": 59}, {"Name": "PORT_heist_underwater", "DlcName": "basegame", "ModificationsCount": 5}, {"Name": "TUNNEL_orange_exterior", "DlcName": "basegame", "ModificationsCount": 17}, {"Name": "hillstunnel", "DlcName": "basegame", "ModificationsCount": 8}, {"Name": "jewelry_entrance_INT", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "jewelry_entrance", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "jewelry_entrance_INT_fog", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "TUNNEL_yellow_ext", "DlcName": "basegame", "ModificationsCount": 12}, {"Name": "NEW_jewel_EXIT", "DlcName": "basegame", "ModificationsCount": 35}, {"Name": "services_nightlight", "DlcName": "basegame", "ModificationsCount": 8}, {"Name": "CS1_railwayB_tunnel", "DlcName": "basegame", "ModificationsCount": 21}, {"Name": "TUNNEL_green_ext", "DlcName": "basegame", "ModificationsCount": 8}, {"Name": "CAMERA_secuirity", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "CAMERA_secuirity_FUZZ", "DlcName": "basegame", "ModificationsCount": 39}, {"Name": "int_hospital_small", "DlcName": "basegame", "ModificationsCount": 32}, {"Name": "int_hospital_dark", "DlcName": "basegame", "ModificationsCount": 32}, {"Name": "plaza_carpark", "DlcName": "basegame", "ModificationsCount": 27}, {"Name": "gen_bank", "DlcName": "basegame", "ModificationsCount": 27}, {"Name": "nightvision", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "WATER_cove", "DlcName": "basegame", "ModificationsCount": 116}, {"Name": "glasses_Darkblue", "DlcName": "basegame", "ModificationsCount": 15}, {"Name": "glasses_VISOR", "DlcName": "basegame", "ModificationsCount": 18}, {"Name": "heist_boat", "DlcName": "basegame", "ModificationsCount": 15}, {"Name": "heist_boat_engineRoom", "DlcName": "basegame", "ModificationsCount": 15}, {"Name": "buggy_shack", "DlcName": "basegame", "ModificationsCount": 28}, {"Name": "mineshaft", "DlcName": "basegame", "ModificationsCount": 29}, {"Name": "NG_first", "DlcName": "basegame", "ModificationsCount": 11}, {"Name": "glasses_Scuba", "DlcName": "basegame", "ModificationsCount": 9}, {"Name": "mugShot", "DlcName": "basegame", "ModificationsCount": 53}, {"Name": "Glasses_BlackOut", "DlcName": "basegame", "ModificationsCount": 8}, {"Name": "winning_room", "DlcName": "basegame", "ModificationsCount": 136}, {"Name": "mugShot_lineup", "DlcName": "basegame", "ModificationsCount": 54}, {"Name": "MPApartHigh_palnning", "DlcName": "basegame", "ModificationsCount": 57}, {"Name": "v_dark", "DlcName": "basegame", "ModificationsCount": 50}, {"Name": "polluted", "DlcName": "basegame", "ModificationsCount": 14}, {"Name": "lightning", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "torpedo", "DlcName": "basegame", "ModificationsCount": 12}, {"Name": "NEW_shrinksOffice", "DlcName": "basegame", "ModificationsCount": 54}, {"Name": "Facebook_NEW", "DlcName": "basegame", "ModificationsCount": 26}, {"Name": "NEW_trevorstrailer", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "New_sewers", "DlcName": "basegame", "ModificationsCount": 55}, {"Name": "facebook_serveroom", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "V_Office_smoke_Fire", "DlcName": "basegame", "ModificationsCount": 17}, {"Name": "V_FIB_IT3_alt5", "DlcName": "basegame", "ModificationsCount": 23}, {"Name": "int_Hospital_DM", "DlcName": "basegame", "ModificationsCount": 20}, {"Name": "int_Hospital2_DM", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "int_Barber1", "DlcName": "basegame", "ModificationsCount": 27}, {"Name": "int_tattoo_B", "DlcName": "basegame", "ModificationsCount": 32}, {"Name": "glasses_black", "DlcName": "basegame", "ModificationsCount": 15}, {"Name": "glasses_brown", "DlcName": "basegame", "ModificationsCount": 12}, {"Name": "glasses_blue", "DlcName": "basegame", "ModificationsCount": 15}, {"Name": "glasses_red", "DlcName": "basegame", "ModificationsCount": 15}, {"Name": "glasses_green", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "glasses_yellow", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "glasses_purple", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "glasses_pink", "DlcName": "basegame", "ModificationsCount": 26}, {"Name": "glasses_orange", "DlcName": "basegame", "ModificationsCount": 26}, {"Name": "WATER_ID2_21", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "WATER_RichmanStuntJump", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "CH3_06_water", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "WATER_refmap_hollywoodlake", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "WATER_CH2_06_01_03", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "WATER_CH2_06_02", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "WATER_CH2_06_04", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "RemoteSniper", "DlcName": "basegame", "ModificationsCount": 38}, {"Name": "V_Office_smoke", "DlcName": "basegame", "ModificationsCount": 22}, {"Name": "V_Office_smoke_ext", "DlcName": "basegame", "ModificationsCount": 17}, {"Name": "V_FIB_IT3", "DlcName": "basegame", "ModificationsCount": 19}, {"Name": "V_FIB_IT3_alt", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "V_FIB_stairs", "DlcName": "basegame", "ModificationsCount": 22}, {"Name": "v_abattoir", "DlcName": "basegame", "ModificationsCount": 23}, {"Name": "V_Abattoir_Cold", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "v_recycle", "DlcName": "basegame", "ModificationsCount": 29}, {"Name": "v_strip3", "DlcName": "basegame", "ModificationsCount": 28}, {"Name": "v_strpchangerm", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "v_jewel2", "DlcName": "basegame", "ModificationsCount": 17}, {"Name": "v_foundry", "DlcName": "basegame", "ModificationsCount": 63}, {"Name": "V_Metro_station", "DlcName": "basegame", "ModificationsCount": 19}, {"Name": "v_metro", "DlcName": "basegame", "ModificationsCount": 22}, {"Name": "V_Metro2", "DlcName": "basegame", "ModificationsCount": 18}, {"Name": "v_torture", "DlcName": "basegame", "ModificationsCount": 61}, {"Name": "v_sweat", "DlcName": "basegame", "ModificationsCount": 23}, {"Name": "v_sweat_entrance", "DlcName": "basegame", "ModificationsCount": 19}, {"Name": "v_sweat_NoDirLight", "DlcName": "basegame", "ModificationsCount": 26}, {"Name": "Barry1_Stoned", "DlcName": "basegame", "ModificationsCount": 21}, {"Name": "v_rockclub", "DlcName": "basegame", "ModificationsCount": 32}, {"Name": "v_michael", "DlcName": "basegame", "ModificationsCount": 16}, {"Name": "v_michael_lounge", "DlcName": "basegame", "ModificationsCount": 6}, {"Name": "v_janitor", "DlcName": "basegame", "ModificationsCount": 22}, {"Name": "int_amb_mult_large", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "int_extlight_large", "DlcName": "basegame", "ModificationsCount": 21}, {"Name": "ext_int_extlight_large", "DlcName": "basegame", "ModificationsCount": 10}, {"Name": "int_extlight_small", "DlcName": "basegame", "ModificationsCount": 27}, {"Name": "int_extlight_small_clipped", "DlcName": "basegame", "ModificationsCount": 18}, {"Name": "int_extlight_large_fog", "DlcName": "basegame", "ModificationsCount": 23}, {"Name": "int_extlight_small_fog", "DlcName": "basegame", "ModificationsCount": 23}, {"Name": "int_extlight_none", "DlcName": "basegame", "ModificationsCount": 20}, {"Name": "int_extlight_none_dark", "DlcName": "basegame", "ModificationsCount": 20}, {"Name": "int_extlight_none_dark_fog", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "int_extlight_none_fog", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "int_clean_extlight_large", "DlcName": "basegame", "ModificationsCount": 18}, {"Name": "int_clean_extlight_small", "DlcName": "basegame", "ModificationsCount": 18}, {"Name": "int_clean_extlight_none", "DlcName": "basegame", "ModificationsCount": 18}, {"Name": "prologue", "DlcName": "basegame", "ModificationsCount": 10}, {"Name": "vagos_extlight_small", "DlcName": "basegame", "ModificationsCount": 23}, {"Name": "FinaleBank", "DlcName": "basegame", "ModificationsCount": 28}, {"Name": "FinaleBankMid", "DlcName": "basegame", "ModificationsCount": 28}, {"Name": "v_cashdepot", "DlcName": "basegame", "ModificationsCount": 23}, {"Name": "V_Solomon<PERSON>", "DlcName": "basegame", "ModificationsCount": 19}, {"Name": "int_methlab_small", "DlcName": "basegame", "ModificationsCount": 41}, {"Name": "int_Lost_small", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "int_Lost_none", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "int_ControlTower_small", "DlcName": "basegame", "ModificationsCount": 27}, {"Name": "int_ControlTower_none", "DlcName": "basegame", "ModificationsCount": 27}, {"Name": "int_dockcontrol_small", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "int_hanger_small", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "int_hanger_none", "DlcName": "basegame", "ModificationsCount": 19}, {"Name": "int_cluckinfactory_small", "DlcName": "basegame", "ModificationsCount": 28}, {"Name": "int_cluckinfactory_none", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "int_FranklinAunt_small", "DlcName": "basegame", "ModificationsCount": 21}, {"Name": "stc_franklinsHouse", "DlcName": "basegame", "ModificationsCount": 9}, {"Name": "stc_coroners", "DlcName": "basegame", "ModificationsCount": 14}, {"Name": "stc_trevors", "DlcName": "basegame", "ModificationsCount": 47}, {"Name": "stc_deviant_lounge", "DlcName": "basegame", "ModificationsCount": 8}, {"Name": "stc_deviant_bedroom", "DlcName": "basegame", "ModificationsCount": 9}, {"Name": "int_carshowroom", "DlcName": "basegame", "ModificationsCount": 37}, {"Name": "int_Farmhouse_small", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "int_Farmhouse_none", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "int_carmod_small", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "SP1_03_drawDistance", "DlcName": "basegame", "ModificationsCount": 5}, {"Name": "int_clotheslow_large", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "v_bahama", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "gunclub", "DlcName": "basegame", "ModificationsCount": 39}, {"Name": "int_GasStation", "DlcName": "basegame", "ModificationsCount": 26}, {"Name": "PoliceStation", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "PoliceStationDark", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "Shop247", "DlcName": "basegame", "ModificationsCount": 29}, {"Name": "Shop247_none", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "<PERSON><PERSON>", "DlcName": "basegame", "ModificationsCount": 22}, {"Name": "cBank_back", "DlcName": "basegame", "ModificationsCount": 22}, {"Name": "cBank_front", "DlcName": "basegame", "ModificationsCount": 27}, {"Name": "int_office_Lobby", "DlcName": "basegame", "ModificationsCount": 21}, {"Name": "int_office_LobbyHall", "DlcName": "basegame", "ModificationsCount": 19}, {"Name": "SheriffStation", "DlcName": "basegame", "ModificationsCount": 18}, {"Name": "LifeInvaderLOD", "DlcName": "basegame", "ModificationsCount": 35}, {"Name": "int_motelroom", "DlcName": "basegame", "ModificationsCount": 21}, {"Name": "metro", "DlcName": "basegame", "ModificationsCount": 23}, {"Name": "int_ClothesHi", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "FIB_5", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "int_chopshop", "DlcName": "basegame", "ModificationsCount": 29}, {"Name": "int_tattoo", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "gunstore", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "int_Hospital_Blue", "DlcName": "basegame", "ModificationsCount": 45}, {"Name": "FIB_6", "DlcName": "basegame", "ModificationsCount": 44}, {"Name": "FIB_B", "DlcName": "basegame", "ModificationsCount": 19}, {"Name": "FIB_A", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "lab_none", "DlcName": "basegame", "ModificationsCount": 19}, {"Name": "lab_none_dark", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "lab_none_dark_fog", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "MP_Garage_L", "DlcName": "basegame", "ModificationsCount": 43}, {"Name": "MP_Studio_Lo", "DlcName": "basegame", "ModificationsCount": 35}, {"Name": "StadLobby", "DlcName": "basegame", "ModificationsCount": 35}, {"Name": "Hanger_INTmods", "DlcName": "basegame", "ModificationsCount": 29}, {"Name": "MPApartHigh", "DlcName": "basegame", "ModificationsCount": 56}, {"Name": "int_Hospital_BlueB", "DlcName": "basegame", "ModificationsCount": 46}, {"Name": "int_tunnel_none_dark", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "MP_lowgarage", "DlcName": "basegame", "ModificationsCount": 36}, {"Name": "MP_MedGarage", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "shades_yellow", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "shades_pink", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "Mp_apart_mid", "DlcName": "basegame", "ModificationsCount": 36}, {"Name": "yell_tunnel_nodirect", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "int_carrier_hanger", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "int_carrier_stair", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "int_carrier_rear", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "int_carrier_control", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "int_carrier_control_2", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "default", "DlcName": "basegame", "ModificationsCount": 0}, {"Name": "gunshop", "DlcName": "basegame", "ModificationsCount": 17}, {"Name": "MichaelsDirectional", "DlcName": "basegame", "ModificationsCount": 17}, {"Name": "Bank_HLWD", "DlcName": "basegame", "ModificationsCount": 32}, {"Name": "MichaelsNODirectional", "DlcName": "basegame", "ModificationsCount": 17}, {"Name": "MichaelsDarkroom", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "int_lesters", "DlcName": "basegame", "ModificationsCount": 22}, {"Name": "Tunnel_green1", "DlcName": "basegame", "ModificationsCount": 18}, {"Name": "cinema_001", "DlcName": "basegame", "ModificationsCount": 27}, {"Name": "exile1_plane", "DlcName": "basegame", "ModificationsCount": 45}, {"Name": "player_transition", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "player_transition_no_scanlines", "DlcName": "basegame", "ModificationsCount": 32}, {"Name": "player_transition_scanlines", "DlcName": "basegame", "ModificationsCount": 5}, {"Name": "switch_cam_1", "DlcName": "basegame", "ModificationsCount": 28}, {"Name": "switch_cam_2", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "Bloom", "DlcName": "basegame", "ModificationsCount": 3}, {"Name": "BloomLight", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "BloomMid", "DlcName": "basegame", "ModificationsCount": 7}, {"Name": "DrivingFocusLight", "DlcName": "basegame", "ModificationsCount": 27}, {"Name": "DrivingFocusDark", "DlcName": "basegame", "ModificationsCount": 27}, {"Name": "RaceTurboLight", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "RaceTurboDark", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "BulletTimeLight", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "BulletTimeDark", "DlcName": "basegame", "ModificationsCount": 28}, {"Name": "REDMIST", "DlcName": "basegame", "ModificationsCount": 56}, {"Name": "REDMIST_blend", "DlcName": "basegame", "ModificationsCount": 57}, {"Name": "MP_<PERSON>_tost", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "MP_Bull_tost_blend", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "MP_Powerplay", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "MP_Powerplay_blend", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "MP_Killstreak", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "MP_Killstreak_blend", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "MP_Loser", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "MP_Loser_blend", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "CHOP", "DlcName": "basegame", "ModificationsCount": 27}, {"Name": "FranklinColorCode", "DlcName": "basegame", "ModificationsCount": 35}, {"Name": "MichaelColorCode", "DlcName": "basegame", "ModificationsCount": 35}, {"Name": "TrevorColorCode", "DlcName": "basegame", "ModificationsCount": 35}, {"Name": "NeutralColorCode", "DlcName": "basegame", "ModificationsCount": 35}, {"Name": "NeutralColorCodeLight", "DlcName": "basegame", "ModificationsCount": 17}, {"Name": "FranklinColorCodeBasic", "DlcName": "basegame", "ModificationsCount": 8}, {"Name": "MichaelColorCodeBasic", "DlcName": "basegame", "ModificationsCount": 8}, {"Name": "TrevorColorCodeBasic", "DlcName": "basegame", "ModificationsCount": 8}, {"Name": "NeutralColorCodeBasic", "DlcName": "basegame", "ModificationsCount": 8}, {"Name": "DefaultColorCode", "DlcName": "basegame", "ModificationsCount": 19}, {"Name": "PlayerSwitchPulse", "DlcName": "basegame", "ModificationsCount": 10}, {"Name": "PlayerSwitchNeutralFlash", "DlcName": "basegame", "ModificationsCount": 13}, {"Name": "hud_def_lensdistortion", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "hud_def_lensdistortion_subtle", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "hud_def_blur", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "hud_def_colorgrade", "DlcName": "basegame", "ModificationsCount": 29}, {"Name": "hud_def_flash", "DlcName": "basegame", "ModificationsCount": 13}, {"Name": "hud_def_desatcrunch", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "hud_def_desat_switch", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "hud_def_desat_cold", "DlcName": "basegame", "ModificationsCount": 37}, {"Name": "hud_def_desat_cold_kill", "DlcName": "basegame", "ModificationsCount": 40}, {"Name": "hud_def_desat_Neutral", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "hud_def_focus", "DlcName": "basegame", "ModificationsCount": 24}, {"Name": "hud_def_desat_<PERSON>", "DlcName": "basegame", "ModificationsCount": 38}, {"Name": "hud_def_desat_Michael", "DlcName": "basegame", "ModificationsCount": 38}, {"Name": "hud_def_desat_<PERSON>", "DlcName": "basegame", "ModificationsCount": 38}, {"Name": "hud_def_<PERSON>", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "hud_def_<PERSON>", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "hud_def_<PERSON>", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DlcName": "basegame", "ModificationsCount": 59}, {"Name": "michealspliff_blend", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "michealspliff_blend02", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "trevor<PERSON><PERSON>", "DlcName": "basegame", "ModificationsCount": 59}, {"Name": "trevorspliff_blend", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "trevorspliff_blend02", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "BarryFadeOut", "DlcName": "basegame", "ModificationsCount": 12}, {"Name": "stoned", "DlcName": "basegame", "ModificationsCount": 42}, {"Name": "stoned_cutscene", "DlcName": "basegame", "ModificationsCount": 20}, {"Name": "stoned_monkeys", "DlcName": "basegame", "ModificationsCount": 21}, {"Name": "stoned_aliens", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "Drunk", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "drug_flying_base", "DlcName": "basegame", "ModificationsCount": 49}, {"Name": "drug_flying_01", "DlcName": "basegame", "ModificationsCount": 4}, {"Name": "drug_flying_02", "DlcName": "basegame", "ModificationsCount": 4}, {"Name": "DRUG_gas_huffin", "DlcName": "basegame", "ModificationsCount": 53}, {"Name": "Drug_deadman", "DlcName": "basegame", "ModificationsCount": 37}, {"Name": "Drug_deadman_blend", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "DRUG_2_drive", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "drug_drive_blend01", "DlcName": "basegame", "ModificationsCount": 32}, {"Name": "drug_drive_blend02", "DlcName": "basegame", "ModificationsCount": 35}, {"Name": "drug_wobbly", "DlcName": "basegame", "ModificationsCount": 56}, {"Name": "Dont_tazeme_bro", "DlcName": "basegame", "ModificationsCount": 22}, {"Name": "dont_tazeme_bro_b", "DlcName": "basegame", "ModificationsCount": 22}, {"Name": "int_extlght_sm_cntrst", "DlcName": "basegame", "ModificationsCount": 22}, {"Name": "MP_heli_cam", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "helicamfirst", "DlcName": "basegame", "ModificationsCount": 11}, {"Name": "introblue", "DlcName": "basegame", "ModificationsCount": 16}, {"Name": "MP_select", "DlcName": "basegame", "ModificationsCount": 4}, {"Name": "PERSHING_water_reflect", "DlcName": "basegame", "ModificationsCount": 1}, {"Name": "exile1_exit", "DlcName": "basegame", "ModificationsCount": 43}, {"Name": "phone_cam", "DlcName": "basegame", "ModificationsCount": 9}, {"Name": "ExplosionJosh", "DlcName": "basegame", "ModificationsCount": 10}, {"Name": "RaceTurboFlash", "DlcName": "basegame", "ModificationsCount": 13}, {"Name": "MP_death_grade", "DlcName": "basegame", "ModificationsCount": 37}, {"Name": "MP_death_grade_blend01", "DlcName": "basegame", "ModificationsCount": 37}, {"Name": "MP_death_grade_blend02", "DlcName": "basegame", "ModificationsCount": 7}, {"Name": "NG_deathfail_BW_base", "DlcName": "basegame", "ModificationsCount": 40}, {"Name": "NG_deathfail_BW_blend01", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "NG_deathfail_BW_blend02", "DlcName": "basegame", "ModificationsCount": 2}, {"Name": "MP_job_win", "DlcName": "basegame", "ModificationsCount": 35}, {"Name": "MP_job_lose", "DlcName": "basegame", "ModificationsCount": 35}, {"Name": "MP_corona_tournament", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "MP_corona_heist", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "MP_corona_selection", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "WhiteOut", "DlcName": "basegame", "ModificationsCount": 5}, {"Name": "BlackOut", "DlcName": "basegame", "ModificationsCount": 10}, {"Name": "MP_job_load", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "MP_intro_logo", "DlcName": "basegame", "ModificationsCount": 22}, {"Name": "MP_corona_switch", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "MP_race_finish", "DlcName": "basegame", "ModificationsCount": 29}, {"Name": "phone_cam1", "DlcName": "basegame", "ModificationsCount": 41}, {"Name": "phone_cam2", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "phone_cam3", "DlcName": "basegame", "ModificationsCount": 41}, {"Name": "phone_cam4", "DlcName": "basegame", "ModificationsCount": 28}, {"Name": "phone_cam5", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "phone_cam6", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "phone_cam7", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "phone_cam9", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "phone_cam10", "DlcName": "basegame", "ModificationsCount": 41}, {"Name": "phone_cam11", "DlcName": "basegame", "ModificationsCount": 27}, {"Name": "phone_cam12", "DlcName": "basegame", "ModificationsCount": 27}, {"Name": "phone_cam13", "DlcName": "basegame", "ModificationsCount": 25}, {"Name": "FranklinColorCodeBright", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "MichaelColorCodeBright", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "TrevorColorCodeBright", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "NeutralColorCodeBright", "DlcName": "basegame", "ModificationsCount": 30}, {"Name": "<PERSON><PERSON><PERSON>m", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "MP_job_load_01", "DlcName": "basegame", "ModificationsCount": 37}, {"Name": "MP_job_load_02", "DlcName": "basegame", "ModificationsCount": 40}, {"Name": "MP_job_preload", "DlcName": "basegame", "ModificationsCount": 37}, {"Name": "MP_job_preload_blend", "DlcName": "basegame", "ModificationsCount": 37}, {"Name": "NG_filmnoir_BW01", "DlcName": "basegame", "ModificationsCount": 36}, {"Name": "NG_filmnoir_BW02", "DlcName": "basegame", "ModificationsCount": 36}, {"Name": "NG_filmic01", "DlcName": "basegame", "ModificationsCount": 32}, {"Name": "NG_filmic02", "DlcName": "basegame", "ModificationsCount": 63}, {"Name": "NG_filmic03", "DlcName": "basegame", "ModificationsCount": 34}, {"Name": "NG_filmic04", "DlcName": "basegame", "ModificationsCount": 36}, {"Name": "NG_filmic05", "DlcName": "basegame", "ModificationsCount": 43}, {"Name": "NG_filmic06", "DlcName": "basegame", "ModificationsCount": 37}, {"Name": "NG_filmic07", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "NG_filmic08", "DlcName": "basegame", "ModificationsCount": 48}, {"Name": "NG_filmic09", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "NG_filmic10", "DlcName": "basegame", "ModificationsCount": 53}, {"Name": "NG_filmic11", "DlcName": "basegame", "ModificationsCount": 45}, {"Name": "NG_filmic12", "DlcName": "basegame", "ModificationsCount": 31}, {"Name": "NG_filmic13", "DlcName": "basegame", "ModificationsCount": 38}, {"Name": "NG_filmic14", "DlcName": "basegame", "ModificationsCount": 41}, {"Name": "NG_filmic15", "DlcName": "basegame", "ModificationsCount": 32}, {"Name": "NG_filmic16", "DlcName": "basegame", "ModificationsCount": 44}, {"Name": "NG_filmic17", "DlcName": "basegame", "ModificationsCount": 33}, {"Name": "NG_filmic18", "DlcName": "basegame", "ModificationsCount": 46}, {"Name": "NG_filmic19", "DlcName": "basegame", "ModificationsCount": 38}, {"Name": "NG_filmic20", "DlcName": "basegame", "ModificationsCount": 44}, {"Name": "underwater", "DlcName": "TitleUpdate", "ModificationsCount": 115}, {"Name": "underwater_deep", "DlcName": "TitleUpdate", "ModificationsCount": 81}, {"Name": "NoAmbientmult", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "superDARK", "DlcName": "TitleUpdate", "ModificationsCount": 3}, {"Name": "CAMERA_BW", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "Forest", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "micheal", "DlcName": "TitleUpdate", "ModificationsCount": 10}, {"Name": "TREVOR", "DlcName": "TitleUpdate", "ModificationsCount": 15}, {"Name": "FRANKLIN", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "Tunnel", "DlcName": "TitleUpdate", "ModificationsCount": 13}, {"Name": "carpark", "DlcName": "TitleUpdate", "ModificationsCount": 18}, {"Name": "NEW_abattoir", "DlcName": "TitleUpdate", "ModificationsCount": 67}, {"Name": "Vagos", "DlcName": "TitleUpdate", "ModificationsCount": 23}, {"Name": "cops", "DlcName": "TitleUpdate", "ModificationsCount": 18}, {"Name": "Bikers", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "BikersSPLASH", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "VagosSPLASH", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "CopsSPLASH", "DlcName": "TitleUpdate", "ModificationsCount": 22}, {"Name": "VAGOS_new_garage", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "VAGOS_new_hangout", "DlcName": "TitleUpdate", "ModificationsCount": 26}, {"Name": "NEW_jewel", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "frankilnsAUNTS_new", "DlcName": "TitleUpdate", "ModificationsCount": 37}, {"Name": "frankilnsAUNTS_SUNdir", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "StreetLighting", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "NEW_tunnels", "DlcName": "TitleUpdate", "ModificationsCount": 26}, {"Name": "NEW_yellowtunnels", "DlcName": "TitleUpdate", "ModificationsCount": 55}, {"Name": "NEW_tunnels_hole", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "NEW_tunnels_ditch", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "Paleto", "DlcName": "TitleUpdate", "ModificationsCount": 6}, {"Name": "new_bank", "DlcName": "TitleUpdate", "ModificationsCount": 17}, {"Name": "ReduceDrawDistance", "DlcName": "TitleUpdate", "ModificationsCount": 10}, {"Name": "ReduceDrawDistanceMission", "DlcName": "TitleUpdate", "ModificationsCount": 6}, {"Name": "lightpolution", "DlcName": "TitleUpdate", "ModificationsCount": 16}, {"Name": "NEW_lesters", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "ReduceDrawDistanceMAP", "DlcName": "TitleUpdate", "ModificationsCount": 6}, {"Name": "reducewaterREF", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "Garage", "DlcName": "TitleUpdate", "ModificationsCount": 12}, {"Name": "LightPollutionHills", "DlcName": "TitleUpdate", "ModificationsCount": 16}, {"Name": "NewMicheal", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "NewMichealupstairs", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "NewMichealstoilet", "DlcName": "TitleUpdate", "ModificationsCount": 18}, {"Name": "NewMichealgirly", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "WATER_port", "DlcName": "TitleUpdate", "ModificationsCount": 60}, {"Name": "WATER_salton", "DlcName": "TitleUpdate", "ModificationsCount": 68}, {"Name": "WATER_river", "DlcName": "TitleUpdate", "ModificationsCount": 61}, {"Name": "FIB_interview", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "NEW_station_unfinished", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "cashdepot", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "cashdepotEMERGENCY", "DlcName": "TitleUpdate", "ModificationsCount": 36}, {"Name": "FrankilinsHOUSEhills", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "HicksbarNEW", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "NOdirectLight", "DlcName": "TitleUpdate", "ModificationsCount": 0}, {"Name": "SALTONSEA", "DlcName": "TitleUpdate", "ModificationsCount": 59}, {"Name": "TUNNEL_green", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "NewMicheal_night", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "WATER_muddy", "DlcName": "TitleUpdate", "ModificationsCount": 69}, {"Name": "WATER_shore", "DlcName": "TitleUpdate", "ModificationsCount": 22}, {"Name": "damage", "DlcName": "TitleUpdate", "ModificationsCount": 11}, {"Name": "hitped", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "dying", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "overwater", "DlcName": "TitleUpdate", "ModificationsCount": 6}, {"Name": "whitenightlighting", "DlcName": "TitleUpdate", "ModificationsCount": 6}, {"Name": "TUNNEL_yellow", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "buildingTOP", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "WATER_lab", "DlcName": "TitleUpdate", "ModificationsCount": 89}, {"Name": "cinema", "DlcName": "TitleUpdate", "ModificationsCount": 36}, {"Name": "fireDEPT", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "ranch", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "TUNNEL_white", "DlcName": "TitleUpdate", "ModificationsCount": 36}, {"Name": "V_recycle_mainroom", "DlcName": "TitleUpdate", "ModificationsCount": 26}, {"Name": "V_recycle_dark", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "V_recycle_light", "DlcName": "TitleUpdate", "ModificationsCount": 26}, {"Name": "lightning_weak", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "lightning_strong", "DlcName": "TitleUpdate", "ModificationsCount": 36}, {"Name": "lightning_cloud", "DlcName": "TitleUpdate", "ModificationsCount": 9}, {"Name": "gunclubrange", "DlcName": "TitleUpdate", "ModificationsCount": 36}, {"Name": "NoAmbientmult_interior", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "FullAmbientmult_interior", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "StreetLightingJunction", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "StreetLightingtraffic", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "Multipayer_spectatorCam", "DlcName": "TitleUpdate", "ModificationsCount": 18}, {"Name": "INT_NoAmbientmult", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "INT_NoAmbientmult_art", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "INT_FullAmbientmult", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "INT_FULLAmbientmult_art", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "INT_FULLAmbientmult_both", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "INT_NoAmbientmult_both", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "<PERSON><PERSON><PERSON>", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "ReduceSSAO", "DlcName": "TitleUpdate", "ModificationsCount": 0}, {"Name": "scope_zoom_in", "DlcName": "TitleUpdate", "ModificationsCount": 5}, {"Name": "scope_zoom_out", "DlcName": "TitleUpdate", "ModificationsCount": 5}, {"Name": "crane_cam", "DlcName": "TitleUpdate", "ModificationsCount": 19}, {"Name": "WATER_silty", "DlcName": "TitleUpdate", "ModificationsCount": 64}, {"Name": "Trevors_room", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "Hint_cam", "DlcName": "TitleUpdate", "ModificationsCount": 16}, {"Name": "venice_canal_tunnel", "DlcName": "TitleUpdate", "ModificationsCount": 47}, {"Name": "blackNwhite", "DlcName": "TitleUpdate", "ModificationsCount": 22}, {"Name": "projector", "DlcName": "TitleUpdate", "ModificationsCount": 12}, {"Name": "paleto_opt", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "warehouse", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "pulse", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "sleeping", "DlcName": "TitleUpdate", "ModificationsCount": 10}, {"Name": "INT_garage", "DlcName": "TitleUpdate", "ModificationsCount": 19}, {"Name": "nextgen", "DlcName": "TitleUpdate", "ModificationsCount": 5}, {"Name": "crane_cam_cinematic", "DlcName": "TitleUpdate", "ModificationsCount": 7}, {"Name": "TUNNEL_orange", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "traffic_skycam", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "powerstation", "DlcName": "TitleUpdate", "ModificationsCount": 7}, {"Name": "SAWMILL", "DlcName": "TitleUpdate", "ModificationsCount": 9}, {"Name": "LODmult_global_reduce", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "LODmult_HD_orphan_reduce", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "LODmult_HD_orphan_LOD_reduce", "DlcName": "TitleUpdate", "ModificationsCount": 3}, {"Name": "LODmult_LOD_reduce", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "LODmult_SLOD1_reduce", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "LODmult_SLOD2_reduce", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "LODmult_SLOD3_reduce", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "NewMicheal_upstairs", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "micheals_lightsOFF", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "telescope", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "WATER_silverlake", "DlcName": "TitleUpdate", "ModificationsCount": 56}, {"Name": "WATER _lab_cooling", "DlcName": "TitleUpdate", "ModificationsCount": 95}, {"Name": "baseTONEMAPPING", "DlcName": "TitleUpdate", "ModificationsCount": 16}, {"Name": "WATER_salton_bottom", "DlcName": "TitleUpdate", "ModificationsCount": 37}, {"Name": "new_stripper_changing", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "underwater_deep_clear", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "prologue_ending_fog", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "graveyard_shootout", "DlcName": "TitleUpdate", "ModificationsCount": 16}, {"Name": "morebloom", "DlcName": "TitleUpdate", "ModificationsCount": 3}, {"Name": "LIGHTSreduceFALLOFF", "DlcName": "TitleUpdate", "ModificationsCount": 0}, {"Name": "INT_posh_hairdresser", "DlcName": "TitleUpdate", "ModificationsCount": 23}, {"Name": "V_strip_office", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "sunglasses", "DlcName": "TitleUpdate", "ModificationsCount": 15}, {"Name": "vespucci_garage", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "half_direct", "DlcName": "TitleUpdate", "ModificationsCount": 3}, {"Name": "carpark_dt1_03", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "tunnel_id1_11", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "reducelightingcost", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "NOrain", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "morgue_dark", "DlcName": "TitleUpdate", "ModificationsCount": 44}, {"Name": "CS3_rail_tunnel", "DlcName": "TitleUpdate", "ModificationsCount": 19}, {"Name": "new_tunnels_entrance", "DlcName": "TitleUpdate", "ModificationsCount": 47}, {"Name": "spectator1", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "spectator2", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "spectator3", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "spectator4", "DlcName": "TitleUpdate", "ModificationsCount": 36}, {"Name": "spectator5", "DlcName": "TitleUpdate", "ModificationsCount": 63}, {"Name": "spectator6", "DlcName": "TitleUpdate", "ModificationsCount": 43}, {"Name": "spectator7", "DlcName": "TitleUpdate", "ModificationsCount": 48}, {"Name": "spectator8", "DlcName": "TitleUpdate", "ModificationsCount": 37}, {"Name": "spectator9", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "spectator10", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "INT_NOdirectLight", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "WATER_resevoir", "DlcName": "TitleUpdate", "ModificationsCount": 68}, {"Name": "WATER_hills", "DlcName": "TitleUpdate", "ModificationsCount": 66}, {"Name": "WATER_militaryPOOP", "DlcName": "TitleUpdate", "ModificationsCount": 62}, {"Name": "NEW_ornate_bank", "DlcName": "TitleUpdate", "ModificationsCount": 43}, {"Name": "NEW_ornate_bank_safe", "DlcName": "TitleUpdate", "ModificationsCount": 46}, {"Name": "NEW_ornate_bank_entrance", "DlcName": "TitleUpdate", "ModificationsCount": 38}, {"Name": "NEW_ornate_bank_office", "DlcName": "TitleUpdate", "ModificationsCount": 46}, {"Name": "LODmult_global_reduce_NOHD", "DlcName": "TitleUpdate", "ModificationsCount": 6}, {"Name": "interior_WATER_lighting", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "gorge_reflectionoffset", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "eyeINtheSKY", "DlcName": "TitleUpdate", "ModificationsCount": 17}, {"Name": "resvoire_reflection", "DlcName": "TitleUpdate", "ModificationsCount": 6}, {"Name": "NO_weather", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "prologue_ext_art_amb", "DlcName": "TitleUpdate", "ModificationsCount": 12}, {"Name": "prologue_shootout", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "heathaze", "DlcName": "TitleUpdate", "ModificationsCount": 22}, {"Name": "KT_underpass", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "INT_nowaterREF", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "carMOD_underpass", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "refit", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "NO_streetAmbient", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "NO_coronas", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "epsilion", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "WATER_refmap_high", "DlcName": "TitleUpdate", "ModificationsCount": 0}, {"Name": "WATER_refmap_med", "DlcName": "TitleUpdate", "ModificationsCount": 0}, {"Name": "WATER_refmap_low", "DlcName": "TitleUpdate", "ModificationsCount": 0}, {"Name": "WATER_refmap_verylow", "DlcName": "TitleUpdate", "ModificationsCount": 0}, {"Name": "WATER_refmap_poolside", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "WATER_refmap_silverlake", "DlcName": "TitleUpdate", "ModificationsCount": 0}, {"Name": "WATER_refmap_venice", "DlcName": "TitleUpdate", "ModificationsCount": 0}, {"Name": "FORdoron_delete", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "NO_fog_alpha", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "V_strip_nofog", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "METRO_Tunnels", "DlcName": "TitleUpdate", "ModificationsCount": 59}, {"Name": "METRO_Tunnels_entrance", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "METRO_platform", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "STRIP_stage", "DlcName": "TitleUpdate", "ModificationsCount": 55}, {"Name": "STRIP_office", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "STRIP_changing", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "INT_NO_fogALPHA", "DlcName": "TitleUpdate", "ModificationsCount": 3}, {"Name": "STRIP_nofog", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "INT_streetlighting", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "ch2_tunnel_whitelight", "DlcName": "TitleUpdate", "ModificationsCount": 6}, {"Name": "AmbientPUSH", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "ship_lighting", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "powerplant_nightlight", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "paleto_nightlight", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "militarybase_nightlight", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "sandyshore_nightlight", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "jewel_gas", "DlcName": "TitleUpdate", "ModificationsCount": 0}, {"Name": "WATER_refmap_off", "DlcName": "TitleUpdate", "ModificationsCount": 13}, {"Name": "trailer_explosion_optimise", "DlcName": "TitleUpdate", "ModificationsCount": 19}, {"Name": "nervousRON_fog", "DlcName": "TitleUpdate", "ModificationsCount": 7}, {"Name": "DONT_overide_sunpos", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "gallery_refmod", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "prison_nightlight", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "multiplayer_ped_fight", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "ship_explosion_underwater", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "EXTRA_bouncelight", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "secret_camera", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "canyon_mission", "DlcName": "TitleUpdate", "ModificationsCount": 23}, {"Name": "gorge_reflection_gpu", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "subBASE_water_ref", "DlcName": "TitleUpdate", "ModificationsCount": 13}, {"Name": "poolsidewaterreflection2", "DlcName": "TitleUpdate", "ModificationsCount": 13}, {"Name": "CUSTOM_streetlight", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "ufo", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "ship_explosion_underwater", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "lab_none_exit", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "FinaleBankexit", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "prologue_reflection_opt", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "tunnel_entrance", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "tunnel_entrance_INT", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "id1_11_tunnel", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "reflection_correct_ambient", "DlcName": "TitleUpdate", "ModificationsCount": 3}, {"Name": "scanline_cam_cheap", "DlcName": "TitleUpdate", "ModificationsCount": 12}, {"Name": "scanline_cam", "DlcName": "TitleUpdate", "ModificationsCount": 16}, {"Name": "VC_tunnel_entrance", "DlcName": "TitleUpdate", "ModificationsCount": 0}, {"Name": "WATER_REF_malibu", "DlcName": "TitleUpdate", "ModificationsCount": 13}, {"Name": "carpark_dt1_02", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "FIB_interview_optimise", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "Prologue_shootout_opt", "DlcName": "TitleUpdate", "ModificationsCount": 10}, {"Name": "hangar_lightsmod", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "plane_inside_mode", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "eatra_bouncelight_beach", "DlcName": "TitleUpdate", "ModificationsCount": 5}, {"Name": "downtown_FIB_cascades_opt", "DlcName": "TitleUpdate", "ModificationsCount": 3}, {"Name": "jewel_optim", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "gorge_reflectionoffset2", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "ufo_deathray", "DlcName": "TitleUpdate", "ModificationsCount": 59}, {"Name": "PORT_heist_underwater", "DlcName": "TitleUpdate", "ModificationsCount": 5}, {"Name": "TUNNEL_orange_exterior", "DlcName": "TitleUpdate", "ModificationsCount": 17}, {"Name": "hillstunnel", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "jewelry_entrance_INT", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "jewelry_entrance", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "jewelry_entrance_INT_fog", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "TUNNEL_yellow_ext", "DlcName": "TitleUpdate", "ModificationsCount": 12}, {"Name": "NEW_jewel_EXIT", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "services_nightlight", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "CS1_railwayB_tunnel", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "TUNNEL_green_ext", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "CAMERA_secuirity", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "CAMERA_secuirity_FUZZ", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "int_hospital_small", "DlcName": "TitleUpdate", "ModificationsCount": 26}, {"Name": "int_hospital_dark", "DlcName": "TitleUpdate", "ModificationsCount": 26}, {"Name": "plaza_carpark", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "gen_bank", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "nightvision", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "WATER_cove", "DlcName": "TitleUpdate", "ModificationsCount": 116}, {"Name": "glasses_Darkblue", "DlcName": "TitleUpdate", "ModificationsCount": 15}, {"Name": "glasses_VISOR", "DlcName": "TitleUpdate", "ModificationsCount": 18}, {"Name": "heist_boat", "DlcName": "TitleUpdate", "ModificationsCount": 15}, {"Name": "heist_boat_norain", "DlcName": "TitleUpdate", "ModificationsCount": 16}, {"Name": "heist_boat_engineRoom", "DlcName": "TitleUpdate", "ModificationsCount": 15}, {"Name": "buggy_shack", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "mineshaft", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "NG_first", "DlcName": "TitleUpdate", "ModificationsCount": 11}, {"Name": "glasses_Scuba", "DlcName": "TitleUpdate", "ModificationsCount": 9}, {"Name": "mugShot", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "Glasses_BlackOut", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "winning_room", "DlcName": "TitleUpdate", "ModificationsCount": 136}, {"Name": "mugShot_lineup", "DlcName": "TitleUpdate", "ModificationsCount": 54}, {"Name": "MPApartHigh_palnning", "DlcName": "TitleUpdate", "ModificationsCount": 56}, {"Name": "v_dark", "DlcName": "TitleUpdate", "ModificationsCount": 67}, {"Name": "vehicle_subint", "DlcName": "TitleUpdate", "ModificationsCount": 12}, {"Name": "Carpark_MP_exit", "DlcName": "TitleUpdate", "ModificationsCount": 13}, {"Name": "EXT_FULLAmbientmult_art", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "new_MP_Garage_L", "DlcName": "TitleUpdate", "ModificationsCount": 45}, {"Name": "fp_vig_black", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "fp_vig_brown", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "fp_vig_gray", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "fp_vig_blue", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "fp_vig_red", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "fp_vig_green", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "INT_trailer_cinema", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "heliGunCam", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "INT_mall", "DlcName": "TitleUpdate", "ModificationsCount": 48}, {"Name": "Mp_<PERSON>ilts", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "Mp_Stilts_gym", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "Mp_Stilts2", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "Mp_Stilts_gym2", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "MPApart_H_01", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "MPApart_H_01_gym", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "MP_H_01_Study", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "MP_H_01_Bedroom", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "MP_H_01_Bathroom", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "MP_H_01_New", "DlcName": "TitleUpdate", "ModificationsCount": 52}, {"Name": "MP_H_01_New_Bedroom", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "MP_H_01_New_Bathroom", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "MP_H_01_New_Study", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "INT_smshop_inMOD", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "NoPedLight", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "morgue_dark_ovr", "DlcName": "TitleUpdate", "ModificationsCount": 45}, {"Name": "INT_smshop_outdoor_bloom", "DlcName": "TitleUpdate", "ModificationsCount": 3}, {"Name": "INT_smshop_indoor_bloom", "DlcName": "TitleUpdate", "ModificationsCount": 3}, {"Name": "MP_H_02", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "MP_H_04", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "Mp_Stilts2_bath", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "mp_h_05", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "mp_h_07", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "MP_H_06", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "mp_h_08", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "yacht_DLC", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "mp_exec_office_01", "DlcName": "TitleUpdate", "ModificationsCount": 26}, {"Name": "mp_exec_warehouse_01", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "mp_exec_office_02", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "mp_exec_office_03", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "mp_exec_office_04", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "mp_exec_office_05", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "mp_exec_office_06", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "mp_exec_office_03_blue", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "mp_exec_office_03C", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "mp_bkr_int01_garage", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "mp_bkr_int01_transition", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "mp_bkr_int01_small_rooms", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "mp_bkr_int02_garage", "DlcName": "TitleUpdate", "ModificationsCount": 23}, {"Name": "mp_bkr_int02_hangout", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "mp_bkr_int02_small_rooms", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "mp_bkr_ware01", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "mp_bkr_ware02_standard", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "mp_bkr_ware02_upgrade", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "mp_bkr_ware02_dry", "DlcName": "TitleUpdate", "ModificationsCount": 22}, {"Name": "mp_bkr_ware03_basic", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "mp_bkr_ware03_upgrade", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "mp_bkr_ware04", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "mp_bkr_ware05", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "mp_lad_night", "DlcName": "TitleUpdate", "ModificationsCount": 7}, {"Name": "mp_lad_day", "DlcName": "TitleUpdate", "ModificationsCount": 7}, {"Name": "mp_lad_judgment", "DlcName": "TitleUpdate", "ModificationsCount": 7}, {"Name": "mp_imx_intwaremed", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "mp_imx_intwaremed_office", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "mp_imx_mod_int_01", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "IMpExt_Interior_02", "DlcName": "TitleUpdate", "ModificationsCount": 57}, {"Name": "ImpExp_Interior_01", "DlcName": "TitleUpdate", "ModificationsCount": 55}, {"Name": "impexp_interior_01_lift", "DlcName": "TitleUpdate", "ModificationsCount": 56}, {"Name": "IMpExt_Interior_02_stair_cage", "DlcName": "TitleUpdate", "ModificationsCount": 57}, {"Name": "mp_gr_int01_white", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "mp_gr_int01_grey", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "mp_gr_int01_black", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "grdlc_int_02", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "mp_nightshark_shield_fp", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "grdlc_int_02_trailer_cave", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "mp_smg_int01_han", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "mp_smg_int01_han_red", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "mp_smg_int01_han_blue", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "mp_smg_int01_han_yellow", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "mp_x17dlc_in_sub", "DlcName": "TitleUpdate", "ModificationsCount": 37}, {"Name": "mp_x17dlc_in_sub_no_reflection", "DlcName": "TitleUpdate", "ModificationsCount": 36}, {"Name": "mp_x17dlc_base", "DlcName": "TitleUpdate", "ModificationsCount": 38}, {"Name": "mp_x17dlc_base_dark", "DlcName": "TitleUpdate", "ModificationsCount": 38}, {"Name": "mp_x17dlc_base_darkest", "DlcName": "TitleUpdate", "ModificationsCount": 38}, {"Name": "mp_x17dlc_lab", "DlcName": "TitleUpdate", "ModificationsCount": 47}, {"Name": "mp_x17dlc_lab_loading_bay", "DlcName": "TitleUpdate", "ModificationsCount": 47}, {"Name": "mp_x17dlc_facility", "DlcName": "TitleUpdate", "ModificationsCount": 47}, {"Name": "mp_x17dlc_facility_conference", "DlcName": "TitleUpdate", "ModificationsCount": 47}, {"Name": "mp_x17dlc_int_01", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint1", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint2", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint3", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint4", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint5", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint6", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint7", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint8", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint9", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_facility2", "DlcName": "TitleUpdate", "ModificationsCount": 47}, {"Name": "mp_x17dlc_int_02", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_02_tint1", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_02_tint2", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_02_tint3", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_02_tint4", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_02_tint5", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_02_tint6", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_02_tint7", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_02_tint8", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_02_tint9", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_02_vehicle_workshop_camera", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "mp_x17dlc_int_02_vehicle_avenger_camera", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "mp_x17dlc_int_02_weapon_avenger_camera", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "mp_x17dlc_int_02_outdoor_intro_camera", "DlcName": "TitleUpdate", "ModificationsCount": 5}, {"Name": "mp_x17dlc_int_silo", "DlcName": "TitleUpdate", "ModificationsCount": 47}, {"Name": "mp_x17dlc_int_silo_escape", "DlcName": "TitleUpdate", "ModificationsCount": 47}, {"Name": "mp_battle_int01", "DlcName": "TitleUpdate", "ModificationsCount": 42}, {"Name": "mp_battle_int01_entry", "DlcName": "TitleUpdate", "ModificationsCount": 42}, {"Name": "mp_battle_int01_office", "DlcName": "TitleUpdate", "ModificationsCount": 40}, {"Name": "mp_battle_int01_dancefloor", "DlcName": "TitleUpdate", "ModificationsCount": 43}, {"Name": "mp_battle_int01_dancefloor_OFF", "DlcName": "TitleUpdate", "ModificationsCount": 44}, {"Name": "mp_battle_int01_garage", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "mp_battle_int02", "DlcName": "TitleUpdate", "ModificationsCount": 42}, {"Name": "mp_battle_int03", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "mp_battle_int03_tint1", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "mp_battle_int03_tint2", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "mp_battle_int03_tint3", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "mp_battle_int03_tint4", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_battle_int03_tint5", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "mp_battle_int03_tint6", "DlcName": "TitleUpdate", "ModificationsCount": 49}, {"Name": "mp_battle_int03_tint7", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "mp_battle_int03_tint8", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "mp_battle_int03_tint9", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "<PERSON>one_FishEye_Lens", "DlcName": "TitleUpdate", "ModificationsCount": 5}, {"Name": "int_arena_Mod", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "int_arena_01", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "int_arena_VIP", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "MP_Arena_VIP", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "int_arena_Mod_garage", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "MP_Arena_theme_midday", "DlcName": "TitleUpdate", "ModificationsCount": 13}, {"Name": "MP_Arena_theme_morning", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "MP_Arena_theme_evening", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "MP_Arena_theme_night", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "MP_Arena_theme_atlantis", "DlcName": "TitleUpdate", "ModificationsCount": 93}, {"Name": "MP_Arena_theme_toxic", "DlcName": "TitleUpdate", "ModificationsCount": 70}, {"Name": "MP_Arena_theme_storm", "DlcName": "TitleUpdate", "ModificationsCount": 76}, {"Name": "MP_Arena_theme_sandstorm", "DlcName": "TitleUpdate", "ModificationsCount": 74}, {"Name": "MP_Arena_theme_saccharine", "DlcName": "TitleUpdate", "ModificationsCount": 72}, {"Name": "MP_Arena_theme_hell", "DlcName": "TitleUpdate", "ModificationsCount": 73}, {"Name": "MP_Arena_theme_scifi_night", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "dlc_casino_carpark", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "casino_mainfloor", "DlcName": "TitleUpdate", "ModificationsCount": 77}, {"Name": "casino_hotel", "DlcName": "TitleUpdate", "ModificationsCount": 58}, {"Name": "casino_brightroom", "DlcName": "TitleUpdate", "ModificationsCount": 73}, {"Name": "DLC_Casino_Garage", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "MP_casino_apartment_MBed", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "MP_casino_apartment_Bath", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "MP_casino_apartment_lobby", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "MP_casino_apartment_office", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "MP_casino_apartment_cinema", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "MP_casino_apartment_bar", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "MP_casino_apartment_spa", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "MP_casino_apartment_lounge", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "MP_casino_apartment_exec", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "MP_casino_apartment_changing", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "casino_managersoffice", "DlcName": "TitleUpdate", "ModificationsCount": 75}, {"Name": "casino_managementOff", "DlcName": "TitleUpdate", "ModificationsCount": 81}, {"Name": "casino_mainWhiteFloor", "DlcName": "TitleUpdate", "ModificationsCount": 77}, {"Name": "CasinoBathrooms", "DlcName": "TitleUpdate", "ModificationsCount": 77}, {"Name": "casino_managementlobby", "DlcName": "TitleUpdate", "ModificationsCount": 81}, {"Name": "MP_casino_apartment_barPARTY", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "MP_casino_apartment_colour1", "DlcName": "TitleUpdate", "ModificationsCount": 10}, {"Name": "MP_casino_apartment_colour0", "DlcName": "TitleUpdate", "ModificationsCount": 9}, {"Name": "NewMod", "DlcName": "TitleUpdate", "ModificationsCount": 0}, {"Name": "MP_casino_apartment_colour2", "DlcName": "TitleUpdate", "ModificationsCount": 9}, {"Name": "MP_casino_apartment_barPARTY_0", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "MP_casino_apartment_barPARTY_2", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "MP_casino_apartment_barPARTY_01", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "Casino_Lightsoff", "DlcName": "TitleUpdate", "ModificationsCount": 40}, {"Name": "MP_Arcade_Retro", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "MP_casino_BACK_office", "DlcName": "TitleUpdate", "ModificationsCount": 42}, {"Name": "MP_casino_BACK_Lobby", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "MP_casino_BACK_coridor", "DlcName": "TitleUpdate", "ModificationsCount": 36}, {"Name": "MP_casino_BACK_Security", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "MP_casino_BACK_BakSideRMs", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "MP_casino_BACK_Waste", "DlcName": "TitleUpdate", "ModificationsCount": 48}, {"Name": "MP_casino_BACK_Laundry", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "MP_casino_BACK_Changing", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "Mp_Heist3_Loading_MainRoom", "DlcName": "TitleUpdate", "ModificationsCount": 26}, {"Name": "Mp_Heist3_Loading_SecurityRoom", "DlcName": "TitleUpdate", "ModificationsCount": 26}, {"Name": "Mp_Heist3_Loading_Stairs", "DlcName": "TitleUpdate", "ModificationsCount": 26}, {"Name": "Mp_Heist3_Loading_Tunnel", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "Mp_Heist3_Utility_Room", "DlcName": "TitleUpdate", "ModificationsCount": 26}, {"Name": "MP_Arcade_Hipster", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "MP_Arcade_Derelict", "DlcName": "TitleUpdate", "ModificationsCount": 36}, {"Name": "MP_Arcade_Tunnel", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "MP_Arcade_Planning_Room", "DlcName": "TitleUpdate", "ModificationsCount": 74}, {"Name": "mp_heist3_lift_shaft", "DlcName": "TitleUpdate", "ModificationsCount": 26}, {"Name": "MP_Arcade_Planning_Pre", "DlcName": "TitleUpdate", "ModificationsCount": 63}, {"Name": "mp_casino_back_vault_rm", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "mp_arc_planning_entity_ambient", "DlcName": "TitleUpdate", "ModificationsCount": 9}, {"Name": "casino_main_floor_heist", "DlcName": "TitleUpdate", "ModificationsCount": 77}, {"Name": "mp_casino_back_staff_entrance", "DlcName": "TitleUpdate", "ModificationsCount": 77}, {"Name": "mp_casino_vault_vault", "DlcName": "TitleUpdate", "ModificationsCount": 85}, {"Name": "mp_casino_vault_corridor", "DlcName": "TitleUpdate", "ModificationsCount": 86}, {"Name": "mp_casino_vault_main", "DlcName": "TitleUpdate", "ModificationsCount": 85}, {"Name": "mp_casino_vault_storage", "DlcName": "TitleUpdate", "ModificationsCount": 86}, {"Name": "MP_Casino_Tunnel", "DlcName": "TitleUpdate", "ModificationsCount": 56}, {"Name": "MP_Casino_Tunnel_Transition", "DlcName": "TitleUpdate", "ModificationsCount": 51}, {"Name": "bokeh_removebuzz", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "int_01_blackbox", "DlcName": "TitleUpdate", "ModificationsCount": 80}, {"Name": "Yacht_Mission_ThunderRain", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "Yacht_Mission_NoLig", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "h4_beachparty", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "h4_beachparty_day", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "DLC_Island_Office", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "dlc_island_vault", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "h4_sub_vehiclebay", "DlcName": "TitleUpdate", "ModificationsCount": 38}, {"Name": "h4_sub_main", "DlcName": "TitleUpdate", "ModificationsCount": 38}, {"Name": "h4_sub_commandroom", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "h4_sub_commandroom_BloomRedux", "DlcName": "TitleUpdate", "ModificationsCount": 36}, {"Name": "DLC_Island_Storerooms", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "DLC_Island_vault_01", "DlcName": "TitleUpdate", "ModificationsCount": 18}, {"Name": "Mpheist4_Mansion_exterior", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "MP_Heist4_Island_Mansion_Ext", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "MP_Heist4_Docks_Ext", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "MP_Heist4_Artificial_Amb_Mul", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "DLC_Island_Storerooms2", "DlcName": "TitleUpdate", "ModificationsCount": 19}, {"Name": "DLC_Island_main_hanger", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "DLC_Island_main_hanger_office", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "DLC_Island_main_hanger_door", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "mp_heist4_office_door", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "MP_Heist4_Storeroom_Door", "DlcName": "TitleUpdate", "ModificationsCount": 19}, {"Name": "DLC_Island_vault_01_Door", "DlcName": "TitleUpdate", "ModificationsCount": 18}, {"Name": "H4_<PERSON>er_for_CU", "DlcName": "TitleUpdate", "ModificationsCount": 18}, {"Name": "underwater_PC_FPS_Heistfix", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "MP_He<PERSON>_Tuner_SmShop_Light", "DlcName": "TitleUpdate", "ModificationsCount": 45}, {"Name": "MP_He<PERSON>_Tuner_SmShop_Dark", "DlcName": "TitleUpdate", "ModificationsCount": 44}, {"Name": "MP_Heist_Tuner_Sandbox", "DlcName": "TitleUpdate", "ModificationsCount": 132}, {"Name": "MP_Heist_Tuner_Meet", "DlcName": "TitleUpdate", "ModificationsCount": 132}, {"Name": "MP_He<PERSON>_<PERSON><PERSON>_SmShop_Default", "DlcName": "TitleUpdate", "ModificationsCount": 44}, {"Name": "MP_He<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "DlcName": "TitleUpdate", "ModificationsCount": 40}, {"Name": "INT_smshop", "DlcName": "TitleUpdate", "ModificationsCount": 38}, {"Name": "MP_He<PERSON>_Tuner_SmShop_WildStyle", "DlcName": "TitleUpdate", "ModificationsCount": 45}, {"Name": "MP_He<PERSON>_Tuner_SmShop_Gamer", "DlcName": "TitleUpdate", "ModificationsCount": 44}, {"Name": "MP_He<PERSON>_Tu<PERSON>_SmShop_Grey", "DlcName": "TitleUpdate", "ModificationsCount": 44}, {"Name": "MP_He<PERSON>_Tuner_SmShop_Nostalgia", "DlcName": "TitleUpdate", "ModificationsCount": 44}, {"Name": "MP_He<PERSON>_Tu<PERSON>_SmShop_Americana", "DlcName": "TitleUpdate", "ModificationsCount": 45}, {"Name": "MP_He<PERSON>_<PERSON><PERSON>_SmShop_JapTunner", "DlcName": "TitleUpdate", "ModificationsCount": 44}, {"Name": "MP_Heist_Tuner_Meet_High_Ref", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "MP_Heist_Tuner_Meet_Low_Ref", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "MP_He<PERSON>_Tuner_Sandbox_Low_Ref", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "DLC_MpSecurity_Studio_Foyer", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "DLC_MpSecurity_Studio_Hall01", "DlcName": "TitleUpdate", "ModificationsCount": 37}, {"Name": "DLC_MpSecurity_Studio_Hall02", "DlcName": "TitleUpdate", "ModificationsCount": 36}, {"Name": "DLC_MpSecurity_Studio_Lounge", "DlcName": "TitleUpdate", "ModificationsCount": 95}, {"Name": "DLC_MpSecurity_Studio_Lounge_sm", "DlcName": "TitleUpdate", "ModificationsCount": 50}, {"Name": "DLC_MpSecurity_Studio_MixingRM", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "DLC_MpSecurity_Studio_RecRM01", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "DLC_MpSecurity_Studio_RecRM02", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "DLC_MpSecurity_Studio_RecRM03", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "DLC_MpSecurity_office_main_floor", "DlcName": "TitleUpdate", "ModificationsCount": 22}, {"Name": "DLC_MpSecurity_Garage", "DlcName": "TitleUpdate", "ModificationsCount": 44}, {"Name": "DLC_MpSecurity_office_Stairs", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "DLC_MpSecurity_office_Bedroom", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "DLC_MpSecurity_office_WorkSpace", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "DLC_MpSecurity_Penthouse_Party", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "DLC_MpSecurity_Stu_Wardrobe", "DlcName": "TitleUpdate", "ModificationsCount": 5}, {"Name": "DLC_MpSecurity_FrankOff_Door", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "DLC_MpSecurity_PlayerOff_Door", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "DLC_MpSecurity_MeetingrOff_Door", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "DLC_MpSecurity_FIX_SET_MCS1", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "Truck_Int_FIX_TRIP1_MCS2", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "DLC_MpSecurity_Studio_MonitorEx", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "DLC_mpSum2_Basement_Entity1", "DlcName": "TitleUpdate", "ModificationsCount": 37}, {"Name": "DLC_mpSum2_Basement_Entity2", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "DLC_mpSum2_Basement_Entity3", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "DLC_mpSum2_Basement_Entity4", "DlcName": "TitleUpdate", "ModificationsCount": 96}, {"Name": "DLC_mpSum2_Basement_Entity5", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "DLC_mpSum2_ULP_01", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "DLC_mpSum2_No_Rain", "DlcName": "TitleUpdate", "ModificationsCount": 3}, {"Name": "MP_Sum2_Warehouse_No_Ambient", "DlcName": "TitleUpdate", "ModificationsCount": 10}, {"Name": "MP_Sum2_ULP_Rooftop_Cascade", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "MP_Xmas2022_Pharma_Lab", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "MP_Xmas2022_Pharma_Lab_Stairs", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "DLC_mpChristmas3_Warehouse", "DlcName": "TitleUpdate", "ModificationsCount": 46}, {"Name": "DLC_mpChristmas3_WarehouseOffic", "DlcName": "TitleUpdate", "ModificationsCount": 43}, {"Name": "MP_Xmas2022_Pharma_Lab_Stairs_B", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "DLC_mpChristmas3_LSDTRUCK", "DlcName": "TitleUpdate", "ModificationsCount": 46}, {"Name": "MP_Xmas2022_Garage", "DlcName": "TitleUpdate", "ModificationsCount": 48}, {"Name": "DLC_Xmas_2022_cargo_plane_2", "DlcName": "TitleUpdate", "ModificationsCount": 44}, {"Name": "Christmas3_GargReveal", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "MP_Xmas2022_Garage_01", "DlcName": "TitleUpdate", "ModificationsCount": 48}, {"Name": "MP_Xmas2022_Garage_02", "DlcName": "TitleUpdate", "ModificationsCount": 48}, {"Name": "Christmas_COOK_MCS1_BlkOut", "DlcName": "TitleUpdate", "ModificationsCount": 22}, {"Name": "DLC_Xmas_2022_cargo_plane_2A", "DlcName": "TitleUpdate", "ModificationsCount": 19}, {"Name": "DLC_Xmas_2022_lab_exit", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "RemoteSniper", "DlcName": "TitleUpdate", "ModificationsCount": 38}, {"Name": "V_Office_smoke", "DlcName": "TitleUpdate", "ModificationsCount": 22}, {"Name": "V_Office_smoke_ext", "DlcName": "TitleUpdate", "ModificationsCount": 17}, {"Name": "V_FIB_IT3", "DlcName": "TitleUpdate", "ModificationsCount": 19}, {"Name": "V_FIB_IT3_alt", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "V_FIB_stairs", "DlcName": "TitleUpdate", "ModificationsCount": 22}, {"Name": "v_abattoir", "DlcName": "TitleUpdate", "ModificationsCount": 23}, {"Name": "V_Abattoir_Cold", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "v_recycle", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "v_strip3", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "v_strpchangerm", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "v_jewel2", "DlcName": "TitleUpdate", "ModificationsCount": 17}, {"Name": "v_foundry", "DlcName": "TitleUpdate", "ModificationsCount": 63}, {"Name": "V_Metro_station", "DlcName": "TitleUpdate", "ModificationsCount": 19}, {"Name": "v_metro", "DlcName": "TitleUpdate", "ModificationsCount": 22}, {"Name": "V_Metro2", "DlcName": "TitleUpdate", "ModificationsCount": 18}, {"Name": "v_torture", "DlcName": "TitleUpdate", "ModificationsCount": 61}, {"Name": "v_sweat", "DlcName": "TitleUpdate", "ModificationsCount": 23}, {"Name": "v_sweat_entrance", "DlcName": "TitleUpdate", "ModificationsCount": 19}, {"Name": "v_sweat_NoDirLight", "DlcName": "TitleUpdate", "ModificationsCount": 26}, {"Name": "Barry1_Stoned", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "v_rockclub", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "v_michael", "DlcName": "TitleUpdate", "ModificationsCount": 16}, {"Name": "v_michael_lounge", "DlcName": "TitleUpdate", "ModificationsCount": 6}, {"Name": "v_janitor", "DlcName": "TitleUpdate", "ModificationsCount": 22}, {"Name": "int_amb_mult_large", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "int_extlight_large", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "ext_int_extlight_large", "DlcName": "TitleUpdate", "ModificationsCount": 10}, {"Name": "int_extlight_small", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "int_extlight_small_clipped", "DlcName": "TitleUpdate", "ModificationsCount": 18}, {"Name": "int_extlight_large_fog", "DlcName": "TitleUpdate", "ModificationsCount": 23}, {"Name": "int_extlight_small_fog", "DlcName": "TitleUpdate", "ModificationsCount": 23}, {"Name": "int_extlight_none", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "int_extlight_none_dark", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "int_extlight_none_dark_fog", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "int_extlight_none_fog", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "int_clean_extlight_large", "DlcName": "TitleUpdate", "ModificationsCount": 18}, {"Name": "int_clean_extlight_small", "DlcName": "TitleUpdate", "ModificationsCount": 18}, {"Name": "int_clean_extlight_none", "DlcName": "TitleUpdate", "ModificationsCount": 18}, {"Name": "prologue", "DlcName": "TitleUpdate", "ModificationsCount": 10}, {"Name": "vagos_extlight_small", "DlcName": "TitleUpdate", "ModificationsCount": 23}, {"Name": "FinaleBank", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "FinaleBankMid", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "v_cashdepot", "DlcName": "TitleUpdate", "ModificationsCount": 23}, {"Name": "V_Solomon<PERSON>", "DlcName": "TitleUpdate", "ModificationsCount": 19}, {"Name": "int_methlab_small", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "int_Lost_small", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "int_Lost_none", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "int_ControlTower_small", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "int_ControlTower_none", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "int_dockcontrol_small", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "int_hanger_small", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "int_hanger_none", "DlcName": "TitleUpdate", "ModificationsCount": 19}, {"Name": "int_cluckinfactory_small", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "int_cluckinfactory_none", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "int_FranklinAunt_small", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "stc_franklinsHouse", "DlcName": "TitleUpdate", "ModificationsCount": 9}, {"Name": "stc_coroners", "DlcName": "TitleUpdate", "ModificationsCount": 14}, {"Name": "stc_trevors", "DlcName": "TitleUpdate", "ModificationsCount": 47}, {"Name": "stc_deviant_lounge", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "stc_deviant_bedroom", "DlcName": "TitleUpdate", "ModificationsCount": 9}, {"Name": "int_carshowroom", "DlcName": "TitleUpdate", "ModificationsCount": 37}, {"Name": "int_Farmhouse_small", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "int_Farmhouse_none", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "int_carmod_small", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "SP1_03_drawDistance", "DlcName": "TitleUpdate", "ModificationsCount": 5}, {"Name": "int_clotheslow_large", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "v_bahama", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "gunclub", "DlcName": "TitleUpdate", "ModificationsCount": 39}, {"Name": "int_GasStation", "DlcName": "TitleUpdate", "ModificationsCount": 26}, {"Name": "PoliceStation", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "PoliceStationDark", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "Shop247", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "Shop247_none", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "<PERSON><PERSON>", "DlcName": "TitleUpdate", "ModificationsCount": 22}, {"Name": "cBank_back", "DlcName": "TitleUpdate", "ModificationsCount": 22}, {"Name": "cBank_front", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "int_office_Lobby", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "int_office_LobbyHall", "DlcName": "TitleUpdate", "ModificationsCount": 19}, {"Name": "SheriffStation", "DlcName": "TitleUpdate", "ModificationsCount": 18}, {"Name": "LifeInvaderLOD", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "int_motelroom", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "metro", "DlcName": "TitleUpdate", "ModificationsCount": 23}, {"Name": "int_ClothesHi", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "FIB_5", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "int_chopshop", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "int_tattoo", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "gunstore", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "int_Hospital_Blue", "DlcName": "TitleUpdate", "ModificationsCount": 45}, {"Name": "FIB_6", "DlcName": "TitleUpdate", "ModificationsCount": 44}, {"Name": "FIB_B", "DlcName": "TitleUpdate", "ModificationsCount": 19}, {"Name": "FIB_A", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "lab_none", "DlcName": "TitleUpdate", "ModificationsCount": 19}, {"Name": "lab_none_dark", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "lab_none_dark_fog", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "MP_Garage_L", "DlcName": "TitleUpdate", "ModificationsCount": 44}, {"Name": "MP_Studio_Lo", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "StadLobby", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "Hanger_INTmods", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "MPApartHigh", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "int_Hospital_BlueB", "DlcName": "TitleUpdate", "ModificationsCount": 46}, {"Name": "int_tunnel_none_dark", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "MP_lowgarage", "DlcName": "TitleUpdate", "ModificationsCount": 36}, {"Name": "MP_MedGarage", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "shades_yellow", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "shades_pink", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "Mp_apart_mid", "DlcName": "TitleUpdate", "ModificationsCount": 36}, {"Name": "yell_tunnel_nodirect", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "int_carrier_hanger", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "int_carrier_stair", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "int_carrier_rear", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "int_carrier_control", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "int_carrier_control_2", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "AP1_01_C_NoFog", "DlcName": "TitleUpdate", "ModificationsCount": 3}, {"Name": "AP1_01_B_IntRefRange", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "rply_saturation", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "rply_saturation_neg", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "rply_vignette", "DlcName": "TitleUpdate", "ModificationsCount": 6}, {"Name": "rply_vignette_neg", "DlcName": "TitleUpdate", "ModificationsCount": 6}, {"Name": "rply_contrast", "DlcName": "TitleUpdate", "ModificationsCount": 12}, {"Name": "rply_contrast_neg", "DlcName": "TitleUpdate", "ModificationsCount": 12}, {"Name": "rply_brightness", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "rply_brightness_neg", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "rply_motionblur", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "V_CIA_Facility", "DlcName": "TitleUpdate", "ModificationsCount": 56}, {"Name": "default", "DlcName": "TitleUpdate", "ModificationsCount": 0}, {"Name": "gunshop", "DlcName": "TitleUpdate", "ModificationsCount": 17}, {"Name": "MichaelsDirectional", "DlcName": "TitleUpdate", "ModificationsCount": 17}, {"Name": "Bank_HLWD", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "MichaelsNODirectional", "DlcName": "TitleUpdate", "ModificationsCount": 17}, {"Name": "MichaelsDarkroom", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "int_lesters", "DlcName": "TitleUpdate", "ModificationsCount": 22}, {"Name": "Tunnel_green1", "DlcName": "TitleUpdate", "ModificationsCount": 18}, {"Name": "cinema_001", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "exile1_plane", "DlcName": "TitleUpdate", "ModificationsCount": 45}, {"Name": "player_transition", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "player_transition_no_scanlines", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "player_transition_scanlines", "DlcName": "TitleUpdate", "ModificationsCount": 5}, {"Name": "switch_cam_1", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "switch_cam_2", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "Bloom", "DlcName": "TitleUpdate", "ModificationsCount": 5}, {"Name": "BloomLight", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "BloomMid", "DlcName": "TitleUpdate", "ModificationsCount": 7}, {"Name": "DrivingFocusLight", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "DrivingFocusDark", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "RaceTurboLight", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "RaceTurboDark", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "BulletTimeLight", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "BulletTimeDark", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "REDMIST", "DlcName": "TitleUpdate", "ModificationsCount": 56}, {"Name": "REDMIST_blend", "DlcName": "TitleUpdate", "ModificationsCount": 57}, {"Name": "MP_<PERSON>_tost", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "MP_Bull_tost_blend", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "MP_Powerplay", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "MP_Powerplay_blend", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "MP_Killstreak", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "MP_Killstreak_blend", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "MP_Loser", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "MP_Loser_blend", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "CHOP", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "FranklinColorCode", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "MichaelColorCode", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "TrevorColorCode", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "NeutralColorCode", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "NeutralColorCodeLight", "DlcName": "TitleUpdate", "ModificationsCount": 17}, {"Name": "FranklinColorCodeBasic", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "MichaelColorCodeBasic", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "TrevorColorCodeBasic", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "NeutralColorCodeBasic", "DlcName": "TitleUpdate", "ModificationsCount": 8}, {"Name": "DefaultColorCode", "DlcName": "TitleUpdate", "ModificationsCount": 19}, {"Name": "PlayerSwitchPulse", "DlcName": "TitleUpdate", "ModificationsCount": 10}, {"Name": "PlayerSwitchNeutralFlash", "DlcName": "TitleUpdate", "ModificationsCount": 13}, {"Name": "hud_def_lensdistortion", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "hud_def_lensdistortion_subtle", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "hud_def_blur", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "hud_def_blur_switch", "DlcName": "TitleUpdate", "ModificationsCount": 5}, {"Name": "hud_def_colorgrade", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "hud_def_flash", "DlcName": "TitleUpdate", "ModificationsCount": 13}, {"Name": "hud_def_desatcrunch", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "hud_def_desat_switch", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "hud_def_desat_cold", "DlcName": "TitleUpdate", "ModificationsCount": 37}, {"Name": "hud_def_desat_cold_kill", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "hud_def_desat_Neutral", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "hud_def_focus", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "hud_def_desat_<PERSON>", "DlcName": "TitleUpdate", "ModificationsCount": 38}, {"Name": "hud_def_desat_Michael", "DlcName": "TitleUpdate", "ModificationsCount": 38}, {"Name": "hud_def_desat_<PERSON>", "DlcName": "TitleUpdate", "ModificationsCount": 38}, {"Name": "hud_def_<PERSON>", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "hud_def_<PERSON>", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "hud_def_<PERSON>", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DlcName": "TitleUpdate", "ModificationsCount": 62}, {"Name": "michealspliff_blend", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "michealspliff_blend02", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "trevor<PERSON><PERSON>", "DlcName": "TitleUpdate", "ModificationsCount": 60}, {"Name": "trevorspliff_blend", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "trevorspliff_blend02", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "BarryFadeOut", "DlcName": "TitleUpdate", "ModificationsCount": 12}, {"Name": "stoned", "DlcName": "TitleUpdate", "ModificationsCount": 42}, {"Name": "stoned_cutscene", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "stoned_monkeys", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "stoned_aliens", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "Drunk", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "drug_flying_base", "DlcName": "TitleUpdate", "ModificationsCount": 55}, {"Name": "drug_flying_01", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "drug_flying_02", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "DRUG_gas_huffin", "DlcName": "TitleUpdate", "ModificationsCount": 53}, {"Name": "Drug_deadman", "DlcName": "TitleUpdate", "ModificationsCount": 37}, {"Name": "Drug_deadman_blend", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "DRUG_2_drive", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "drug_drive_blend01", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "drug_drive_blend02", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "drug_wobbly", "DlcName": "TitleUpdate", "ModificationsCount": 56}, {"Name": "Dont_tazeme_bro", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "dont_tazeme_bro_b", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "int_extlght_sm_cntrst", "DlcName": "TitleUpdate", "ModificationsCount": 22}, {"Name": "MP_heli_cam", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "helicamfirst", "DlcName": "TitleUpdate", "ModificationsCount": 11}, {"Name": "introblue", "DlcName": "TitleUpdate", "ModificationsCount": 16}, {"Name": "MP_select", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "PERSHING_water_reflect", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "exile1_exit", "DlcName": "TitleUpdate", "ModificationsCount": 43}, {"Name": "phone_cam", "DlcName": "TitleUpdate", "ModificationsCount": 9}, {"Name": "ExplosionJosh", "DlcName": "TitleUpdate", "ModificationsCount": 10}, {"Name": "RaceTurboFlash", "DlcName": "TitleUpdate", "ModificationsCount": 13}, {"Name": "MP_death_grade", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "MP_death_grade_blend01", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "MP_death_grade_blend02", "DlcName": "TitleUpdate", "ModificationsCount": 7}, {"Name": "NG_deathfail_BW_base", "DlcName": "TitleUpdate", "ModificationsCount": 40}, {"Name": "NG_deathfail_BW_blend01", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "NG_deathfail_BW_blend02", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "MP_job_win", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "MP_job_lose", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "MP_corona_tournament", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "MP_corona_tournament_DOF", "DlcName": "TitleUpdate", "ModificationsCount": 37}, {"Name": "MP_corona_heist", "DlcName": "TitleUpdate", "ModificationsCount": 12}, {"Name": "MP_corona_heist_blend", "DlcName": "TitleUpdate", "ModificationsCount": 14}, {"Name": "MP_corona_heist_DOF", "DlcName": "TitleUpdate", "ModificationsCount": 3}, {"Name": "MP_corona_heist_BW", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "MP_corona_selection", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "WhiteOut", "DlcName": "TitleUpdate", "ModificationsCount": 5}, {"Name": "BlackOut", "DlcName": "TitleUpdate", "ModificationsCount": 10}, {"Name": "MP_job_load", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "MP_intro_logo", "DlcName": "TitleUpdate", "ModificationsCount": 22}, {"Name": "MP_corona_switch", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "MP_race_finish", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "phone_cam3_REMOVED", "DlcName": "TitleUpdate", "ModificationsCount": 40}, {"Name": "phone_cam8_REMOVED", "DlcName": "TitleUpdate", "ModificationsCount": 38}, {"Name": "phone_cam1", "DlcName": "TitleUpdate", "ModificationsCount": 39}, {"Name": "phone_cam2", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "phone_cam3", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "phone_cam4", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "phone_cam5", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "phone_cam6", "DlcName": "TitleUpdate", "ModificationsCount": 39}, {"Name": "phone_cam7", "DlcName": "TitleUpdate", "ModificationsCount": 40}, {"Name": "phone_cam8", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "phone_cam9", "DlcName": "TitleUpdate", "ModificationsCount": 42}, {"Name": "phone_cam10", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "phone_cam11", "DlcName": "TitleUpdate", "ModificationsCount": 42}, {"Name": "phone_cam12", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "phone_cam13", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "FranklinColorCodeBright", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "MichaelColorCodeBright", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "TrevorColorCodeBright", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "NeutralColorCodeBright", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "<PERSON><PERSON><PERSON>m", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "MP_job_load_01", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "MP_job_load_02", "DlcName": "TitleUpdate", "ModificationsCount": 38}, {"Name": "MP_job_preload", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "MP_job_preload_blend", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "NG_filmnoir_BW01", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "NG_filmnoir_BW02", "DlcName": "TitleUpdate", "ModificationsCount": 40}, {"Name": "lab_none_exit_OVR", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "lab_none_dark_OVR", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "LectroLight", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "LectroDark", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "NG_filmic01", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "NG_filmic02", "DlcName": "TitleUpdate", "ModificationsCount": 63}, {"Name": "NG_filmic03", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "NG_filmic04", "DlcName": "TitleUpdate", "ModificationsCount": 36}, {"Name": "NG_filmic05", "DlcName": "TitleUpdate", "ModificationsCount": 43}, {"Name": "NG_filmic06", "DlcName": "TitleUpdate", "ModificationsCount": 37}, {"Name": "NG_filmic07", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "NG_filmic08", "DlcName": "TitleUpdate", "ModificationsCount": 48}, {"Name": "NG_filmic09", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "NG_filmic10", "DlcName": "TitleUpdate", "ModificationsCount": 51}, {"Name": "NG_filmic11", "DlcName": "TitleUpdate", "ModificationsCount": 45}, {"Name": "NG_filmic12", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "NG_filmic13", "DlcName": "TitleUpdate", "ModificationsCount": 38}, {"Name": "NG_filmic14", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "NG_filmic15", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "NG_filmic16", "DlcName": "TitleUpdate", "ModificationsCount": 44}, {"Name": "NG_filmic17", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "NG_filmic18", "DlcName": "TitleUpdate", "ModificationsCount": 46}, {"Name": "NG_filmic19", "DlcName": "TitleUpdate", "ModificationsCount": 39}, {"Name": "NG_filmic20", "DlcName": "TitleUpdate", "ModificationsCount": 44}, {"Name": "NG_filmic21", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "NG_filmic22", "DlcName": "TitleUpdate", "ModificationsCount": 43}, {"Name": "NG_filmic23", "DlcName": "TitleUpdate", "ModificationsCount": 34}, {"Name": "NG_filmic24", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "NG_filmic25", "DlcName": "TitleUpdate", "ModificationsCount": 60}, {"Name": "NG_blackout", "DlcName": "TitleUpdate", "ModificationsCount": 9}, {"Name": "MP_deathfail_night", "DlcName": "TitleUpdate", "ModificationsCount": 71}, {"Name": "lodscaler", "DlcName": "TitleUpdate", "ModificationsCount": 7}, {"Name": "maxlodscaler", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "MP_job_preload_night", "DlcName": "TitleUpdate", "ModificationsCount": 46}, {"Name": "MP_job_end_night", "DlcName": "TitleUpdate", "ModificationsCount": 42}, {"Name": "MP_corona_heist_BW_night", "DlcName": "TitleUpdate", "ModificationsCount": 46}, {"Name": "MP_corona_heist_night", "DlcName": "TitleUpdate", "ModificationsCount": 42}, {"Name": "MP_corona_heist_night_blend", "DlcName": "TitleUpdate", "ModificationsCount": 43}, {"Name": "PennedInLight", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "PennedInDark", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "BeastLaunch01", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "BeastLaunch02", "DlcName": "TitleUpdate", "ModificationsCount": 38}, {"Name": "BeastIntro01", "DlcName": "TitleUpdate", "ModificationsCount": 56}, {"Name": "BeastIntro02", "DlcName": "TitleUpdate", "ModificationsCount": 56}, {"Name": "CrossLine01", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "CrossLine02", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "InchOrange01", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "InchOrange02", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "InchPurple01", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "InchPurple02", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "TinyPink01", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "TinyPink02", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "TinyGreen01", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "TinyGreen02", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "InchPickup01", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "InchPickup02", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "PPOrange01", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "PPOrange02", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "PPPurple01", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "PPPurple02", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "PPGreen01", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "PPGreen02", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "PPPink01", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "PPPink02", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "StuntSlowLight", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "StuntSlowDark", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "StuntFastLight", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "StuntFastDark", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "PPFilter", "DlcName": "TitleUpdate", "ModificationsCount": 63}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DlcName": "TitleUpdate", "ModificationsCount": 63}, {"Name": "LostTimeLight", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "LostTimeDark", "DlcName": "TitleUpdate", "ModificationsCount": 25}, {"Name": "LostTimeFlash", "DlcName": "TitleUpdate", "ModificationsCount": 13}, {"Name": "DeadlineNeon01", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "BikerFormFlash", "DlcName": "TitleUpdate", "ModificationsCount": 13}, {"Name": "BikerForm01", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "VolticBlur", "DlcName": "TitleUpdate", "ModificationsCount": 1}, {"Name": "VolticFlash", "DlcName": "TitleUpdate", "ModificationsCount": 14}, {"Name": "VolticGold", "DlcName": "TitleUpdate", "ModificationsCount": 15}, {"Name": "BleepYellow01", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "BleepYellow02", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "TinyRacerMoBlur", "DlcName": "TitleUpdate", "ModificationsCount": 3}, {"Name": "WeaponUpgrade", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "AirRaceBoost01", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "AirRaceBoost02", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "TransformRaceFlash", "DlcName": "TitleUpdate", "ModificationsCount": 15}, {"Name": "BombCamFlash", "DlcName": "TitleUpdate", "ModificationsCount": 13}, {"Name": "BombCam01", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "WarpCheckpoint", "DlcName": "TitleUpdate", "ModificationsCount": 15}, {"Name": "TransformFlash", "DlcName": "TitleUpdate", "ModificationsCount": 12}, {"Name": "SmugglerFlash", "DlcName": "TitleUpdate", "ModificationsCount": 15}, {"Name": "SmugglerCheckpoint01", "DlcName": "TitleUpdate", "ModificationsCount": 6}, {"Name": "SmugglerCheckpoint02", "DlcName": "TitleUpdate", "ModificationsCount": 24}, {"Name": "OrbitalCannon", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "Broken_camera_fuzz", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "RemixDrone", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "ArenaWheelPurple01", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "ArenaWheelPurple02", "DlcName": "TitleUpdate", "ModificationsCount": 27}, {"Name": "ArenaEMP", "DlcName": "TitleUpdate", "ModificationsCount": 56}, {"Name": "ArenaEMP_Blend", "DlcName": "TitleUpdate", "ModificationsCount": 57}, {"Name": "Heist_VaultGas01", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "CarDamageHit", "DlcName": "TitleUpdate", "ModificationsCount": 6}, {"Name": "CarPitstopComplete", "DlcName": "TitleUpdate", "ModificationsCount": 6}, {"Name": "CarPitstopHealth01", "DlcName": "TitleUpdate", "ModificationsCount": 30}, {"Name": "CarPitstopHealth02", "DlcName": "TitleUpdate", "ModificationsCount": 29}, {"Name": "MissileOutOfRange", "DlcName": "TitleUpdate", "ModificationsCount": 26}, {"Name": "CCTV_overlay01", "DlcName": "TitleUpdate", "ModificationsCount": 33}, {"Name": "CCTV_overlay02", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "IslandGlassPlinthHeat", "DlcName": "TitleUpdate", "ModificationsCount": 28}, {"Name": "IslandPeriscope", "DlcName": "TitleUpdate", "ModificationsCount": 36}, {"Name": "Island_CCTV_ChannelFuzz", "DlcName": "TitleUpdate", "ModificationsCount": 26}, {"Name": "Island_CCTV_Overlay", "DlcName": "TitleUpdate", "ModificationsCount": 38}, {"Name": "DanceIntensityFlash", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "DanceIntensity", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "DanceIntensityDistortion", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "Heist4CameraFlash", "DlcName": "TitleUpdate", "ModificationsCount": 20}, {"Name": "DanceIntensityFlash01", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "DanceIntensityFlash02", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "DanceIntensityFlash03", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "DanceIntensityDistortion01", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "DanceIntensityDistortion02", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "DanceIntensityDistortion03", "DlcName": "TitleUpdate", "ModificationsCount": 2}, {"Name": "DanceIntensity01", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "DanceIntensity02", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "DanceIntensity03", "DlcName": "TitleUpdate", "ModificationsCount": 21}, {"Name": "MP_island_heist", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "FixerShortTrip", "DlcName": "TitleUpdate", "ModificationsCount": 41}, {"Name": "FixerShortTrip_Distort", "DlcName": "TitleUpdate", "ModificationsCount": 63}, {"Name": "PlayerWakeUp", "DlcName": "TitleUpdate", "ModificationsCount": 35}, {"Name": "DaxTrip01", "DlcName": "TitleUpdate", "ModificationsCount": 51}, {"Name": "DaxTrip02", "DlcName": "TitleUpdate", "ModificationsCount": 31}, {"Name": "DaxTrip03", "DlcName": "TitleUpdate", "ModificationsCount": 50}, {"Name": "Dax_TripBase", "DlcName": "TitleUpdate", "ModificationsCount": 32}, {"Name": "Dax_TripBlend01", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "Dax_TripBlend02", "DlcName": "TitleUpdate", "ModificationsCount": 4}, {"Name": "li", "DlcName": "mpapartment", "ModificationsCount": 33}, {"Name": "underwater", "DlcName": "mpapartment", "ModificationsCount": 115}, {"Name": "underwater_deep", "DlcName": "mpapartment", "ModificationsCount": 81}, {"Name": "NoAmbientmult", "DlcName": "mpapartment", "ModificationsCount": 4}, {"Name": "superDARK", "DlcName": "mpapartment", "ModificationsCount": 3}, {"Name": "CAMERA_BW", "DlcName": "mpapartment", "ModificationsCount": 33}, {"Name": "Forest", "DlcName": "mpapartment", "ModificationsCount": 30}, {"Name": "micheal", "DlcName": "mpapartment", "ModificationsCount": 10}, {"Name": "TREVOR", "DlcName": "mpapartment", "ModificationsCount": 15}, {"Name": "FRANKLIN", "DlcName": "mpapartment", "ModificationsCount": 20}, {"Name": "Tunnel", "DlcName": "mpapartment", "ModificationsCount": 13}, {"Name": "carpark", "DlcName": "mpapartment", "ModificationsCount": 18}, {"Name": "NEW_abattoir", "DlcName": "mpapartment", "ModificationsCount": 67}, {"Name": "Vagos", "DlcName": "mpapartment", "ModificationsCount": 23}, {"Name": "cops", "DlcName": "mpapartment", "ModificationsCount": 18}, {"Name": "Bikers", "DlcName": "mpapartment", "ModificationsCount": 28}, {"Name": "BikersSPLASH", "DlcName": "mpapartment", "ModificationsCount": 30}, {"Name": "VagosSPLASH", "DlcName": "mpapartment", "ModificationsCount": 21}, {"Name": "CopsSPLASH", "DlcName": "mpapartment", "ModificationsCount": 22}, {"Name": "VAGOS_new_garage", "DlcName": "mpapartment", "ModificationsCount": 24}, {"Name": "VAGOS_new_hangout", "DlcName": "mpapartment", "ModificationsCount": 26}, {"Name": "NEW_jewel", "DlcName": "mpapartment", "ModificationsCount": 35}, {"Name": "frankilnsAUNTS_new", "DlcName": "mpapartment", "ModificationsCount": 37}, {"Name": "frankilnsAUNTS_SUNdir", "DlcName": "mpapartment", "ModificationsCount": 2}, {"Name": "StreetLighting", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "NEW_tunnels", "DlcName": "mpapartment", "ModificationsCount": 26}, {"Name": "NEW_yellowtunnels", "DlcName": "mpapartment", "ModificationsCount": 55}, {"Name": "NEW_tunnels_hole", "DlcName": "mpapartment", "ModificationsCount": 31}, {"Name": "NEW_tunnels_ditch", "DlcName": "mpapartment", "ModificationsCount": 30}, {"Name": "Paleto", "DlcName": "mpapartment", "ModificationsCount": 6}, {"Name": "new_bank", "DlcName": "mpapartment", "ModificationsCount": 17}, {"Name": "ReduceDrawDistance", "DlcName": "mpapartment", "ModificationsCount": 10}, {"Name": "ReduceDrawDistanceMission", "DlcName": "mpapartment", "ModificationsCount": 6}, {"Name": "lightpolution", "DlcName": "mpapartment", "ModificationsCount": 16}, {"Name": "NEW_lesters", "DlcName": "mpapartment", "ModificationsCount": 35}, {"Name": "ReduceDrawDistanceMAP", "DlcName": "mpapartment", "ModificationsCount": 6}, {"Name": "reducewaterREF", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "garage", "DlcName": "mpapartment", "ModificationsCount": 12}, {"Name": "LightPollutionHills", "DlcName": "mpapartment", "ModificationsCount": 16}, {"Name": "NewMicheal", "DlcName": "mpapartment", "ModificationsCount": 30}, {"Name": "NewMichealupstairs", "DlcName": "mpapartment", "ModificationsCount": 20}, {"Name": "NewMichealstoilet", "DlcName": "mpapartment", "ModificationsCount": 18}, {"Name": "NewMichealgirly", "DlcName": "mpapartment", "ModificationsCount": 29}, {"Name": "WATER_port", "DlcName": "mpapartment", "ModificationsCount": 60}, {"Name": "WATER_salton", "DlcName": "mpapartment", "ModificationsCount": 68}, {"Name": "WATER_river", "DlcName": "mpapartment", "ModificationsCount": 61}, {"Name": "FIB_interview", "DlcName": "mpapartment", "ModificationsCount": 33}, {"Name": "NEW_station_unfinished", "DlcName": "mpapartment", "ModificationsCount": 33}, {"Name": "cashdepot", "DlcName": "mpapartment", "ModificationsCount": 30}, {"Name": "cashdepotEMERGENCY", "DlcName": "mpapartment", "ModificationsCount": 36}, {"Name": "FrankilinsHOUSEhills", "DlcName": "mpapartment", "ModificationsCount": 29}, {"Name": "HicksbarNEW", "DlcName": "mpapartment", "ModificationsCount": 32}, {"Name": "NOdirectLight", "DlcName": "mpapartment", "ModificationsCount": 0}, {"Name": "SALTONSEA", "DlcName": "mpapartment", "ModificationsCount": 59}, {"Name": "TUNNEL_green", "DlcName": "mpapartment", "ModificationsCount": 32}, {"Name": "NewMicheal_night", "DlcName": "mpapartment", "ModificationsCount": 24}, {"Name": "WATER_muddy", "DlcName": "mpapartment", "ModificationsCount": 69}, {"Name": "WATER_shore", "DlcName": "mpapartment", "ModificationsCount": 22}, {"Name": "damage", "DlcName": "mpapartment", "ModificationsCount": 11}, {"Name": "hitped", "DlcName": "mpapartment", "ModificationsCount": 2}, {"Name": "dying", "DlcName": "mpapartment", "ModificationsCount": 8}, {"Name": "overwater", "DlcName": "mpapartment", "ModificationsCount": 6}, {"Name": "whitenightlighting", "DlcName": "mpapartment", "ModificationsCount": 6}, {"Name": "TUNNEL_yellow", "DlcName": "mpapartment", "ModificationsCount": 32}, {"Name": "buildingTOP", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "WATER_lab", "DlcName": "mpapartment", "ModificationsCount": 89}, {"Name": "cinema", "DlcName": "mpapartment", "ModificationsCount": 36}, {"Name": "fireDEPT", "DlcName": "mpapartment", "ModificationsCount": 34}, {"Name": "ranch", "DlcName": "mpapartment", "ModificationsCount": 33}, {"Name": "TUNNEL_white", "DlcName": "mpapartment", "ModificationsCount": 36}, {"Name": "V_recycle_mainroom", "DlcName": "mpapartment", "ModificationsCount": 26}, {"Name": "V_recycle_dark", "DlcName": "mpapartment", "ModificationsCount": 27}, {"Name": "V_recycle_light", "DlcName": "mpapartment", "ModificationsCount": 26}, {"Name": "lightning_weak", "DlcName": "mpapartment", "ModificationsCount": 33}, {"Name": "lightning_strong", "DlcName": "mpapartment", "ModificationsCount": 36}, {"Name": "lightning_cloud", "DlcName": "mpapartment", "ModificationsCount": 9}, {"Name": "gunclubrange", "DlcName": "mpapartment", "ModificationsCount": 36}, {"Name": "NoAmbientmult_interior", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "FullAmbientmult_interior", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "StreetLightingJunction", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "StreetLightingtraffic", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "Multipayer_spectatorCam", "DlcName": "mpapartment", "ModificationsCount": 18}, {"Name": "INT_NoAmbientmult", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "INT_NoAmbientmult_art", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "INT_FullAmbientmult", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "INT_FULLAmbientmult_art", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "INT_FULLAmbientmult_both", "DlcName": "mpapartment", "ModificationsCount": 2}, {"Name": "INT_NoAmbientmult_both", "DlcName": "mpapartment", "ModificationsCount": 2}, {"Name": "<PERSON><PERSON><PERSON>", "DlcName": "mpapartment", "ModificationsCount": 17}, {"Name": "ReduceSSAO", "DlcName": "mpapartment", "ModificationsCount": 0}, {"Name": "scope_zoom_in", "DlcName": "mpapartment", "ModificationsCount": 5}, {"Name": "scope_zoom_out", "DlcName": "mpapartment", "ModificationsCount": 5}, {"Name": "crane_cam", "DlcName": "mpapartment", "ModificationsCount": 19}, {"Name": "WATER_silty", "DlcName": "mpapartment", "ModificationsCount": 64}, {"Name": "Trevors_room", "DlcName": "mpapartment", "ModificationsCount": 31}, {"Name": "Hint_cam", "DlcName": "mpapartment", "ModificationsCount": 16}, {"Name": "venice_canal_tunnel", "DlcName": "mpapartment", "ModificationsCount": 47}, {"Name": "blackNwhite", "DlcName": "mpapartment", "ModificationsCount": 22}, {"Name": "projector", "DlcName": "mpapartment", "ModificationsCount": 12}, {"Name": "paleto_opt", "DlcName": "mpapartment", "ModificationsCount": 8}, {"Name": "warehouse", "DlcName": "mpapartment", "ModificationsCount": 27}, {"Name": "pulse", "DlcName": "mpapartment", "ModificationsCount": 20}, {"Name": "sleeping", "DlcName": "mpapartment", "ModificationsCount": 10}, {"Name": "INT_garage", "DlcName": "mpapartment", "ModificationsCount": 19}, {"Name": "nextgen", "DlcName": "mpapartment", "ModificationsCount": 5}, {"Name": "crane_cam_cinematic", "DlcName": "mpapartment", "ModificationsCount": 7}, {"Name": "TUNNEL_orange", "DlcName": "mpapartment", "ModificationsCount": 33}, {"Name": "traffic_skycam", "DlcName": "mpapartment", "ModificationsCount": 30}, {"Name": "powerstation", "DlcName": "mpapartment", "ModificationsCount": 7}, {"Name": "SAWMILL", "DlcName": "mpapartment", "ModificationsCount": 9}, {"Name": "LODmult_global_reduce", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "LODmult_HD_orphan_reduce", "DlcName": "mpapartment", "ModificationsCount": 2}, {"Name": "LODmult_HD_orphan_LOD_reduce", "DlcName": "mpapartment", "ModificationsCount": 3}, {"Name": "LODmult_LOD_reduce", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "LODmult_SLOD1_reduce", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "LODmult_SLOD2_reduce", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "LODmult_SLOD3_reduce", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "NewMicheal_upstairs", "DlcName": "mpapartment", "ModificationsCount": 24}, {"Name": "micheals_lightsOFF", "DlcName": "mpapartment", "ModificationsCount": 2}, {"Name": "telescope", "DlcName": "mpapartment", "ModificationsCount": 22}, {"Name": "WATER_silverlake", "DlcName": "mpapartment", "ModificationsCount": 56}, {"Name": "WATER _lab_cooling", "DlcName": "mpapartment", "ModificationsCount": 95}, {"Name": "baseTONEMAPPING", "DlcName": "mpapartment", "ModificationsCount": 16}, {"Name": "WATER_salton_bottom", "DlcName": "mpapartment", "ModificationsCount": 37}, {"Name": "new_stripper_changing", "DlcName": "mpapartment", "ModificationsCount": 29}, {"Name": "underwater_deep_clear", "DlcName": "mpapartment", "ModificationsCount": 4}, {"Name": "prologue_ending_fog", "DlcName": "mpapartment", "ModificationsCount": 25}, {"Name": "graveyard_shootout", "DlcName": "mpapartment", "ModificationsCount": 16}, {"Name": "morebloom", "DlcName": "mpapartment", "ModificationsCount": 3}, {"Name": "LIGHTSreduceFALLOFF", "DlcName": "mpapartment", "ModificationsCount": 0}, {"Name": "INT_posh_hairdresser", "DlcName": "mpapartment", "ModificationsCount": 23}, {"Name": "V_strip_office", "DlcName": "mpapartment", "ModificationsCount": 28}, {"Name": "sunglasses", "DlcName": "mpapartment", "ModificationsCount": 15}, {"Name": "vespucci_garage", "DlcName": "mpapartment", "ModificationsCount": 34}, {"Name": "half_direct", "DlcName": "mpapartment", "ModificationsCount": 3}, {"Name": "carpark_dt1_03", "DlcName": "mpapartment", "ModificationsCount": 20}, {"Name": "tunnel_id1_11", "DlcName": "mpapartment", "ModificationsCount": 20}, {"Name": "reducelightingcost", "DlcName": "mpapartment", "ModificationsCount": 2}, {"Name": "NOrain", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "morgue_dark", "DlcName": "mpapartment", "ModificationsCount": 44}, {"Name": "CS3_rail_tunnel", "DlcName": "mpapartment", "ModificationsCount": 19}, {"Name": "new_tunnels_entrance", "DlcName": "mpapartment", "ModificationsCount": 47}, {"Name": "spectator1", "DlcName": "mpapartment", "ModificationsCount": 41}, {"Name": "spectator2", "DlcName": "mpapartment", "ModificationsCount": 34}, {"Name": "spectator3", "DlcName": "mpapartment", "ModificationsCount": 33}, {"Name": "spectator4", "DlcName": "mpapartment", "ModificationsCount": 36}, {"Name": "spectator5", "DlcName": "mpapartment", "ModificationsCount": 63}, {"Name": "spectator6", "DlcName": "mpapartment", "ModificationsCount": 43}, {"Name": "spectator7", "DlcName": "mpapartment", "ModificationsCount": 48}, {"Name": "spectator8", "DlcName": "mpapartment", "ModificationsCount": 37}, {"Name": "spectator9", "DlcName": "mpapartment", "ModificationsCount": 53}, {"Name": "spectator10", "DlcName": "mpapartment", "ModificationsCount": 31}, {"Name": "INT_NOdirectLight", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "WATER_resevoir", "DlcName": "mpapartment", "ModificationsCount": 68}, {"Name": "WATER_hills", "DlcName": "mpapartment", "ModificationsCount": 66}, {"Name": "WATER_militaryPOOP", "DlcName": "mpapartment", "ModificationsCount": 62}, {"Name": "NEW_ornate_bank", "DlcName": "mpapartment", "ModificationsCount": 43}, {"Name": "NEW_ornate_bank_safe", "DlcName": "mpapartment", "ModificationsCount": 46}, {"Name": "NEW_ornate_bank_entrance", "DlcName": "mpapartment", "ModificationsCount": 38}, {"Name": "NEW_ornate_bank_office", "DlcName": "mpapartment", "ModificationsCount": 46}, {"Name": "LODmult_global_reduce_NOHD", "DlcName": "mpapartment", "ModificationsCount": 6}, {"Name": "interior_WATER_lighting", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "gorge_reflectionoffset", "DlcName": "mpapartment", "ModificationsCount": 2}, {"Name": "eyeINtheSKY", "DlcName": "mpapartment", "ModificationsCount": 22}, {"Name": "resvoire_reflection", "DlcName": "mpapartment", "ModificationsCount": 6}, {"Name": "NO_weather", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "prologue_ext_art_amb", "DlcName": "mpapartment", "ModificationsCount": 12}, {"Name": "prologue_shootout", "DlcName": "mpapartment", "ModificationsCount": 20}, {"Name": "heathaze", "DlcName": "mpapartment", "ModificationsCount": 22}, {"Name": "KT_underpass", "DlcName": "mpapartment", "ModificationsCount": 25}, {"Name": "INT_nowaterREF", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "carMOD_underpass", "DlcName": "mpapartment", "ModificationsCount": 28}, {"Name": "refit", "DlcName": "mpapartment", "ModificationsCount": 34}, {"Name": "NO_streetAmbient", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "NO_coronas", "DlcName": "mpapartment", "ModificationsCount": 2}, {"Name": "epsilion", "DlcName": "mpapartment", "ModificationsCount": 24}, {"Name": "WATER_refmap_high", "DlcName": "mpapartment", "ModificationsCount": 0}, {"Name": "WATER_refmap_med", "DlcName": "mpapartment", "ModificationsCount": 0}, {"Name": "WATER_refmap_low", "DlcName": "mpapartment", "ModificationsCount": 0}, {"Name": "WATER_refmap_verylow", "DlcName": "mpapartment", "ModificationsCount": 0}, {"Name": "WATER_refmap_poolside", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "WATER_refmap_silverlake", "DlcName": "mpapartment", "ModificationsCount": 0}, {"Name": "WATER_refmap_venice", "DlcName": "mpapartment", "ModificationsCount": 0}, {"Name": "FORdoron_delete", "DlcName": "mpapartment", "ModificationsCount": 4}, {"Name": "NO_fog_alpha", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "V_strip_nofog", "DlcName": "mpapartment", "ModificationsCount": 28}, {"Name": "METRO_Tunnels", "DlcName": "mpapartment", "ModificationsCount": 59}, {"Name": "METRO_Tunnels_entrance", "DlcName": "mpapartment", "ModificationsCount": 31}, {"Name": "METRO_platform", "DlcName": "mpapartment", "ModificationsCount": 30}, {"Name": "STRIP_stage", "DlcName": "mpapartment", "ModificationsCount": 55}, {"Name": "STRIP_office", "DlcName": "mpapartment", "ModificationsCount": 30}, {"Name": "STRIP_changing", "DlcName": "mpapartment", "ModificationsCount": 30}, {"Name": "INT_NO_fogALPHA", "DlcName": "mpapartment", "ModificationsCount": 3}, {"Name": "STRIP_nofog", "DlcName": "mpapartment", "ModificationsCount": 30}, {"Name": "INT_streetlighting", "DlcName": "mpapartment", "ModificationsCount": 8}, {"Name": "ch2_tunnel_whitelight", "DlcName": "mpapartment", "ModificationsCount": 6}, {"Name": "AmbientPUSH", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "ship_lighting", "DlcName": "mpapartment", "ModificationsCount": 4}, {"Name": "powerplant_nightlight", "DlcName": "mpapartment", "ModificationsCount": 8}, {"Name": "paleto_nightlight", "DlcName": "mpapartment", "ModificationsCount": 8}, {"Name": "militarybase_nightlight", "DlcName": "mpapartment", "ModificationsCount": 8}, {"Name": "sandyshore_nightlight", "DlcName": "mpapartment", "ModificationsCount": 8}, {"Name": "jewel_gas", "DlcName": "mpapartment", "ModificationsCount": 0}, {"Name": "WATER_refmap_off", "DlcName": "mpapartment", "ModificationsCount": 13}, {"Name": "trailer_explosion_optimise", "DlcName": "mpapartment", "ModificationsCount": 19}, {"Name": "nervousRON_fog", "DlcName": "mpapartment", "ModificationsCount": 7}, {"Name": "DONT_overide_sunpos", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "gallery_refmod", "DlcName": "mpapartment", "ModificationsCount": 20}, {"Name": "prison_nightlight", "DlcName": "mpapartment", "ModificationsCount": 8}, {"Name": "multiplayer_ped_fight", "DlcName": "mpapartment", "ModificationsCount": 4}, {"Name": "ship_explosion_underwater", "DlcName": "mpapartment", "ModificationsCount": 20}, {"Name": "EXTRA_bouncelight", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "secret_camera", "DlcName": "mpapartment", "ModificationsCount": 33}, {"Name": "canyon_mission", "DlcName": "mpapartment", "ModificationsCount": 23}, {"Name": "gorge_reflection_gpu", "DlcName": "mpapartment", "ModificationsCount": 4}, {"Name": "subBASE_water_ref", "DlcName": "mpapartment", "ModificationsCount": 13}, {"Name": "poolsidewaterreflection2", "DlcName": "mpapartment", "ModificationsCount": 13}, {"Name": "CUSTOM_streetlight", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "ufo", "DlcName": "mpapartment", "ModificationsCount": 32}, {"Name": "ship_explosion_underwater", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "lab_none_exit", "DlcName": "mpapartment", "ModificationsCount": 24}, {"Name": "FinaleBankexit", "DlcName": "mpapartment", "ModificationsCount": 28}, {"Name": "prologue_reflection_opt", "DlcName": "mpapartment", "ModificationsCount": 4}, {"Name": "tunnel_entrance", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "tunnel_entrance_INT", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "id1_11_tunnel", "DlcName": "mpapartment", "ModificationsCount": 31}, {"Name": "reflection_correct_ambient", "DlcName": "mpapartment", "ModificationsCount": 3}, {"Name": "scanline_cam_cheap", "DlcName": "mpapartment", "ModificationsCount": 12}, {"Name": "scanline_cam", "DlcName": "mpapartment", "ModificationsCount": 16}, {"Name": "VC_tunnel_entrance", "DlcName": "mpapartment", "ModificationsCount": 0}, {"Name": "WATER_REF_malibu", "DlcName": "mpapartment", "ModificationsCount": 13}, {"Name": "carpark_dt1_02", "DlcName": "mpapartment", "ModificationsCount": 31}, {"Name": "FIB_interview_optimise", "DlcName": "mpapartment", "ModificationsCount": 21}, {"Name": "Prologue_shootout_opt", "DlcName": "mpapartment", "ModificationsCount": 10}, {"Name": "hangar_lightsmod", "DlcName": "mpapartment", "ModificationsCount": 2}, {"Name": "plane_inside_mode", "DlcName": "mpapartment", "ModificationsCount": 34}, {"Name": "eatra_bouncelight_beach", "DlcName": "mpapartment", "ModificationsCount": 5}, {"Name": "downtown_FIB_cascades_opt", "DlcName": "mpapartment", "ModificationsCount": 3}, {"Name": "jewel_optim", "DlcName": "mpapartment", "ModificationsCount": 2}, {"Name": "gorge_reflectionoffset2", "DlcName": "mpapartment", "ModificationsCount": 2}, {"Name": "ufo_deathray", "DlcName": "mpapartment", "ModificationsCount": 59}, {"Name": "PORT_heist_underwater", "DlcName": "mpapartment", "ModificationsCount": 5}, {"Name": "TUNNEL_orange_exterior", "DlcName": "mpapartment", "ModificationsCount": 17}, {"Name": "hillstunnel", "DlcName": "mpapartment", "ModificationsCount": 8}, {"Name": "jewelry_entrance_INT", "DlcName": "mpapartment", "ModificationsCount": 2}, {"Name": "jewelry_entrance", "DlcName": "mpapartment", "ModificationsCount": 2}, {"Name": "jewelry_entrance_INT_fog", "DlcName": "mpapartment", "ModificationsCount": 2}, {"Name": "TUNNEL_yellow_ext", "DlcName": "mpapartment", "ModificationsCount": 12}, {"Name": "NEW_jewel_EXIT", "DlcName": "mpapartment", "ModificationsCount": 35}, {"Name": "services_nightlight", "DlcName": "mpapartment", "ModificationsCount": 8}, {"Name": "CS1_railwayB_tunnel", "DlcName": "mpapartment", "ModificationsCount": 21}, {"Name": "TUNNEL_green_ext", "DlcName": "mpapartment", "ModificationsCount": 8}, {"Name": "CAMERA_secuirity", "DlcName": "mpapartment", "ModificationsCount": 33}, {"Name": "CAMERA_secuirity_FUZZ", "DlcName": "mpapartment", "ModificationsCount": 39}, {"Name": "int_hospital_small", "DlcName": "mpapartment", "ModificationsCount": 26}, {"Name": "int_hospital_dark", "DlcName": "mpapartment", "ModificationsCount": 26}, {"Name": "plaza_carpark", "DlcName": "mpapartment", "ModificationsCount": 27}, {"Name": "gen_bank", "DlcName": "mpapartment", "ModificationsCount": 27}, {"Name": "nightvision", "DlcName": "mpapartment", "ModificationsCount": 30}, {"Name": "WATER_cove", "DlcName": "mpapartment", "ModificationsCount": 116}, {"Name": "glasses_Darkblue", "DlcName": "mpapartment", "ModificationsCount": 15}, {"Name": "glasses_VISOR", "DlcName": "mpapartment", "ModificationsCount": 18}, {"Name": "heist_boat", "DlcName": "mpapartment", "ModificationsCount": 15}, {"Name": "heist_boat_norain", "DlcName": "mpapartment", "ModificationsCount": 16}, {"Name": "heist_boat_engineRoom", "DlcName": "mpapartment", "ModificationsCount": 15}, {"Name": "buggy_shack", "DlcName": "mpapartment", "ModificationsCount": 28}, {"Name": "mineshaft", "DlcName": "mpapartment", "ModificationsCount": 29}, {"Name": "NG_first", "DlcName": "mpapartment", "ModificationsCount": 11}, {"Name": "glasses_Scuba", "DlcName": "mpapartment", "ModificationsCount": 9}, {"Name": "mugShot", "DlcName": "mpapartment", "ModificationsCount": 53}, {"Name": "Glasses_BlackOut", "DlcName": "mpapartment", "ModificationsCount": 8}, {"Name": "winning_room", "DlcName": "mpapartment", "ModificationsCount": 136}, {"Name": "mugShot_lineup", "DlcName": "mpapartment", "ModificationsCount": 54}, {"Name": "MPApartHigh_palnning", "DlcName": "mpapartment", "ModificationsCount": 57}, {"Name": "v_dark", "DlcName": "mpapartment", "ModificationsCount": 67}, {"Name": "vehicle_subint", "DlcName": "mpapartment", "ModificationsCount": 12}, {"Name": "Carpark_MP_exit", "DlcName": "mpapartment", "ModificationsCount": 13}, {"Name": "EXT_FULLAmbientmult_art", "DlcName": "mpapartment", "ModificationsCount": 1}, {"Name": "li", "DlcName": "mpchristmas2017", "ModificationsCount": 32}, {"Name": "underwater", "DlcName": "mpchristmas2017", "ModificationsCount": 115}, {"Name": "underwater_deep", "DlcName": "mpchristmas2017", "ModificationsCount": 81}, {"Name": "NoAmbientmult", "DlcName": "mpchristmas2017", "ModificationsCount": 4}, {"Name": "superDARK", "DlcName": "mpchristmas2017", "ModificationsCount": 3}, {"Name": "CAMERA_BW", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "Forest", "DlcName": "mpchristmas2017", "ModificationsCount": 30}, {"Name": "micheal", "DlcName": "mpchristmas2017", "ModificationsCount": 10}, {"Name": "TREVOR", "DlcName": "mpchristmas2017", "ModificationsCount": 15}, {"Name": "FRANKLIN", "DlcName": "mpchristmas2017", "ModificationsCount": 20}, {"Name": "Tunnel", "DlcName": "mpchristmas2017", "ModificationsCount": 13}, {"Name": "carpark", "DlcName": "mpchristmas2017", "ModificationsCount": 18}, {"Name": "NEW_abattoir", "DlcName": "mpchristmas2017", "ModificationsCount": 67}, {"Name": "Vagos", "DlcName": "mpchristmas2017", "ModificationsCount": 23}, {"Name": "cops", "DlcName": "mpchristmas2017", "ModificationsCount": 18}, {"Name": "Bikers", "DlcName": "mpchristmas2017", "ModificationsCount": 28}, {"Name": "BikersSPLASH", "DlcName": "mpchristmas2017", "ModificationsCount": 30}, {"Name": "VagosSPLASH", "DlcName": "mpchristmas2017", "ModificationsCount": 21}, {"Name": "CopsSPLASH", "DlcName": "mpchristmas2017", "ModificationsCount": 22}, {"Name": "VAGOS_new_garage", "DlcName": "mpchristmas2017", "ModificationsCount": 24}, {"Name": "VAGOS_new_hangout", "DlcName": "mpchristmas2017", "ModificationsCount": 26}, {"Name": "NEW_jewel", "DlcName": "mpchristmas2017", "ModificationsCount": 35}, {"Name": "frankilnsAUNTS_new", "DlcName": "mpchristmas2017", "ModificationsCount": 37}, {"Name": "frankilnsAUNTS_SUNdir", "DlcName": "mpchristmas2017", "ModificationsCount": 2}, {"Name": "StreetLighting", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "NEW_tunnels", "DlcName": "mpchristmas2017", "ModificationsCount": 26}, {"Name": "NEW_yellowtunnels", "DlcName": "mpchristmas2017", "ModificationsCount": 55}, {"Name": "NEW_tunnels_hole", "DlcName": "mpchristmas2017", "ModificationsCount": 31}, {"Name": "NEW_tunnels_ditch", "DlcName": "mpchristmas2017", "ModificationsCount": 30}, {"Name": "Paleto", "DlcName": "mpchristmas2017", "ModificationsCount": 6}, {"Name": "new_bank", "DlcName": "mpchristmas2017", "ModificationsCount": 17}, {"Name": "ReduceDrawDistance", "DlcName": "mpchristmas2017", "ModificationsCount": 10}, {"Name": "ReduceDrawDistanceMission", "DlcName": "mpchristmas2017", "ModificationsCount": 6}, {"Name": "lightpolution", "DlcName": "mpchristmas2017", "ModificationsCount": 16}, {"Name": "NEW_lesters", "DlcName": "mpchristmas2017", "ModificationsCount": 35}, {"Name": "ReduceDrawDistanceMAP", "DlcName": "mpchristmas2017", "ModificationsCount": 6}, {"Name": "reducewaterREF", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "garage", "DlcName": "mpchristmas2017", "ModificationsCount": 12}, {"Name": "LightPollutionHills", "DlcName": "mpchristmas2017", "ModificationsCount": 16}, {"Name": "NewMicheal", "DlcName": "mpchristmas2017", "ModificationsCount": 30}, {"Name": "NewMichealupstairs", "DlcName": "mpchristmas2017", "ModificationsCount": 20}, {"Name": "NewMichealstoilet", "DlcName": "mpchristmas2017", "ModificationsCount": 18}, {"Name": "NewMichealgirly", "DlcName": "mpchristmas2017", "ModificationsCount": 29}, {"Name": "WATER_port", "DlcName": "mpchristmas2017", "ModificationsCount": 60}, {"Name": "WATER_salton", "DlcName": "mpchristmas2017", "ModificationsCount": 68}, {"Name": "WATER_river", "DlcName": "mpchristmas2017", "ModificationsCount": 61}, {"Name": "FIB_interview", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "NEW_station_unfinished", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "cashdepot", "DlcName": "mpchristmas2017", "ModificationsCount": 30}, {"Name": "cashdepotEMERGENCY", "DlcName": "mpchristmas2017", "ModificationsCount": 36}, {"Name": "FrankilinsHOUSEhills", "DlcName": "mpchristmas2017", "ModificationsCount": 29}, {"Name": "HicksbarNEW", "DlcName": "mpchristmas2017", "ModificationsCount": 32}, {"Name": "NOdirectLight", "DlcName": "mpchristmas2017", "ModificationsCount": 0}, {"Name": "SALTONSEA", "DlcName": "mpchristmas2017", "ModificationsCount": 59}, {"Name": "TUNNEL_green", "DlcName": "mpchristmas2017", "ModificationsCount": 31}, {"Name": "NewMicheal_night", "DlcName": "mpchristmas2017", "ModificationsCount": 24}, {"Name": "WATER_muddy", "DlcName": "mpchristmas2017", "ModificationsCount": 69}, {"Name": "WATER_shore", "DlcName": "mpchristmas2017", "ModificationsCount": 22}, {"Name": "damage", "DlcName": "mpchristmas2017", "ModificationsCount": 11}, {"Name": "hitped", "DlcName": "mpchristmas2017", "ModificationsCount": 2}, {"Name": "dying", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "overwater", "DlcName": "mpchristmas2017", "ModificationsCount": 6}, {"Name": "whitenightlighting", "DlcName": "mpchristmas2017", "ModificationsCount": 6}, {"Name": "TUNNEL_yellow", "DlcName": "mpchristmas2017", "ModificationsCount": 32}, {"Name": "buildingTOP", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "WATER_lab", "DlcName": "mpchristmas2017", "ModificationsCount": 89}, {"Name": "cinema", "DlcName": "mpchristmas2017", "ModificationsCount": 36}, {"Name": "fireDEPT", "DlcName": "mpchristmas2017", "ModificationsCount": 34}, {"Name": "ranch", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "TUNNEL_white", "DlcName": "mpchristmas2017", "ModificationsCount": 36}, {"Name": "V_recycle_mainroom", "DlcName": "mpchristmas2017", "ModificationsCount": 26}, {"Name": "V_recycle_dark", "DlcName": "mpchristmas2017", "ModificationsCount": 27}, {"Name": "V_recycle_light", "DlcName": "mpchristmas2017", "ModificationsCount": 26}, {"Name": "lightning_weak", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "lightning_strong", "DlcName": "mpchristmas2017", "ModificationsCount": 36}, {"Name": "lightning_cloud", "DlcName": "mpchristmas2017", "ModificationsCount": 9}, {"Name": "gunclubrange", "DlcName": "mpchristmas2017", "ModificationsCount": 36}, {"Name": "NoAmbientmult_interior", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "FullAmbientmult_interior", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "StreetLightingJunction", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "StreetLightingtraffic", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "Multipayer_spectatorCam", "DlcName": "mpchristmas2017", "ModificationsCount": 18}, {"Name": "INT_NoAmbientmult", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "INT_NoAmbientmult_art", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "INT_FullAmbientmult", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "INT_FULLAmbientmult_art", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "INT_FULLAmbientmult_both", "DlcName": "mpchristmas2017", "ModificationsCount": 2}, {"Name": "INT_NoAmbientmult_both", "DlcName": "mpchristmas2017", "ModificationsCount": 2}, {"Name": "<PERSON><PERSON><PERSON>", "DlcName": "mpchristmas2017", "ModificationsCount": 20}, {"Name": "ReduceSSAO", "DlcName": "mpchristmas2017", "ModificationsCount": 0}, {"Name": "scope_zoom_in", "DlcName": "mpchristmas2017", "ModificationsCount": 5}, {"Name": "scope_zoom_out", "DlcName": "mpchristmas2017", "ModificationsCount": 5}, {"Name": "crane_cam", "DlcName": "mpchristmas2017", "ModificationsCount": 19}, {"Name": "WATER_silty", "DlcName": "mpchristmas2017", "ModificationsCount": 64}, {"Name": "Trevors_room", "DlcName": "mpchristmas2017", "ModificationsCount": 31}, {"Name": "Hint_cam", "DlcName": "mpchristmas2017", "ModificationsCount": 16}, {"Name": "venice_canal_tunnel", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "blackNwhite", "DlcName": "mpchristmas2017", "ModificationsCount": 22}, {"Name": "projector", "DlcName": "mpchristmas2017", "ModificationsCount": 12}, {"Name": "paleto_opt", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "warehouse", "DlcName": "mpchristmas2017", "ModificationsCount": 27}, {"Name": "pulse", "DlcName": "mpchristmas2017", "ModificationsCount": 20}, {"Name": "sleeping", "DlcName": "mpchristmas2017", "ModificationsCount": 10}, {"Name": "INT_garage", "DlcName": "mpchristmas2017", "ModificationsCount": 19}, {"Name": "nextgen", "DlcName": "mpchristmas2017", "ModificationsCount": 5}, {"Name": "crane_cam_cinematic", "DlcName": "mpchristmas2017", "ModificationsCount": 7}, {"Name": "TUNNEL_orange", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "traffic_skycam", "DlcName": "mpchristmas2017", "ModificationsCount": 30}, {"Name": "powerstation", "DlcName": "mpchristmas2017", "ModificationsCount": 7}, {"Name": "SAWMILL", "DlcName": "mpchristmas2017", "ModificationsCount": 9}, {"Name": "LODmult_global_reduce", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "LODmult_HD_orphan_reduce", "DlcName": "mpchristmas2017", "ModificationsCount": 2}, {"Name": "LODmult_HD_orphan_LOD_reduce", "DlcName": "mpchristmas2017", "ModificationsCount": 3}, {"Name": "LODmult_LOD_reduce", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "LODmult_SLOD1_reduce", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "LODmult_SLOD2_reduce", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "LODmult_SLOD3_reduce", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "NewMicheal_upstairs", "DlcName": "mpchristmas2017", "ModificationsCount": 24}, {"Name": "micheals_lightsOFF", "DlcName": "mpchristmas2017", "ModificationsCount": 2}, {"Name": "telescope", "DlcName": "mpchristmas2017", "ModificationsCount": 25}, {"Name": "WATER_silverlake", "DlcName": "mpchristmas2017", "ModificationsCount": 56}, {"Name": "WATER _lab_cooling", "DlcName": "mpchristmas2017", "ModificationsCount": 95}, {"Name": "baseTONEMAPPING", "DlcName": "mpchristmas2017", "ModificationsCount": 16}, {"Name": "WATER_salton_bottom", "DlcName": "mpchristmas2017", "ModificationsCount": 37}, {"Name": "new_stripper_changing", "DlcName": "mpchristmas2017", "ModificationsCount": 29}, {"Name": "underwater_deep_clear", "DlcName": "mpchristmas2017", "ModificationsCount": 4}, {"Name": "prologue_ending_fog", "DlcName": "mpchristmas2017", "ModificationsCount": 25}, {"Name": "graveyard_shootout", "DlcName": "mpchristmas2017", "ModificationsCount": 16}, {"Name": "morebloom", "DlcName": "mpchristmas2017", "ModificationsCount": 3}, {"Name": "LIGHTSreduceFALLOFF", "DlcName": "mpchristmas2017", "ModificationsCount": 0}, {"Name": "INT_posh_hairdresser", "DlcName": "mpchristmas2017", "ModificationsCount": 23}, {"Name": "V_strip_office", "DlcName": "mpchristmas2017", "ModificationsCount": 28}, {"Name": "sunglasses", "DlcName": "mpchristmas2017", "ModificationsCount": 15}, {"Name": "vespucci_garage", "DlcName": "mpchristmas2017", "ModificationsCount": 34}, {"Name": "half_direct", "DlcName": "mpchristmas2017", "ModificationsCount": 3}, {"Name": "carpark_dt1_03", "DlcName": "mpchristmas2017", "ModificationsCount": 20}, {"Name": "tunnel_id1_11", "DlcName": "mpchristmas2017", "ModificationsCount": 20}, {"Name": "reducelightingcost", "DlcName": "mpchristmas2017", "ModificationsCount": 2}, {"Name": "NOrain", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "morgue_dark", "DlcName": "mpchristmas2017", "ModificationsCount": 44}, {"Name": "CS3_rail_tunnel", "DlcName": "mpchristmas2017", "ModificationsCount": 19}, {"Name": "new_tunnels_entrance", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "spectator1", "DlcName": "mpchristmas2017", "ModificationsCount": 41}, {"Name": "spectator2", "DlcName": "mpchristmas2017", "ModificationsCount": 34}, {"Name": "spectator3", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "spectator4", "DlcName": "mpchristmas2017", "ModificationsCount": 36}, {"Name": "spectator5", "DlcName": "mpchristmas2017", "ModificationsCount": 63}, {"Name": "spectator6", "DlcName": "mpchristmas2017", "ModificationsCount": 43}, {"Name": "spectator7", "DlcName": "mpchristmas2017", "ModificationsCount": 48}, {"Name": "spectator8", "DlcName": "mpchristmas2017", "ModificationsCount": 37}, {"Name": "spectator9", "DlcName": "mpchristmas2017", "ModificationsCount": 53}, {"Name": "spectator10", "DlcName": "mpchristmas2017", "ModificationsCount": 31}, {"Name": "INT_NOdirectLight", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "WATER_resevoir", "DlcName": "mpchristmas2017", "ModificationsCount": 68}, {"Name": "WATER_hills", "DlcName": "mpchristmas2017", "ModificationsCount": 66}, {"Name": "WATER_militaryPOOP", "DlcName": "mpchristmas2017", "ModificationsCount": 62}, {"Name": "NEW_ornate_bank", "DlcName": "mpchristmas2017", "ModificationsCount": 43}, {"Name": "NEW_ornate_bank_safe", "DlcName": "mpchristmas2017", "ModificationsCount": 46}, {"Name": "NEW_ornate_bank_entrance", "DlcName": "mpchristmas2017", "ModificationsCount": 38}, {"Name": "NEW_ornate_bank_office", "DlcName": "mpchristmas2017", "ModificationsCount": 46}, {"Name": "LODmult_global_reduce_NOHD", "DlcName": "mpchristmas2017", "ModificationsCount": 6}, {"Name": "interior_WATER_lighting", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "gorge_reflectionoffset", "DlcName": "mpchristmas2017", "ModificationsCount": 2}, {"Name": "eyeINtheSKY", "DlcName": "mpchristmas2017", "ModificationsCount": 17}, {"Name": "resvoire_reflection", "DlcName": "mpchristmas2017", "ModificationsCount": 6}, {"Name": "NO_weather", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "prologue_ext_art_amb", "DlcName": "mpchristmas2017", "ModificationsCount": 12}, {"Name": "prologue_shootout", "DlcName": "mpchristmas2017", "ModificationsCount": 20}, {"Name": "heathaze", "DlcName": "mpchristmas2017", "ModificationsCount": 22}, {"Name": "KT_underpass", "DlcName": "mpchristmas2017", "ModificationsCount": 25}, {"Name": "INT_nowaterREF", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "carMOD_underpass", "DlcName": "mpchristmas2017", "ModificationsCount": 28}, {"Name": "refit", "DlcName": "mpchristmas2017", "ModificationsCount": 34}, {"Name": "NO_streetAmbient", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "NO_coronas", "DlcName": "mpchristmas2017", "ModificationsCount": 2}, {"Name": "epsilion", "DlcName": "mpchristmas2017", "ModificationsCount": 24}, {"Name": "WATER_refmap_high", "DlcName": "mpchristmas2017", "ModificationsCount": 0}, {"Name": "WATER_refmap_med", "DlcName": "mpchristmas2017", "ModificationsCount": 0}, {"Name": "WATER_refmap_low", "DlcName": "mpchristmas2017", "ModificationsCount": 0}, {"Name": "WATER_refmap_verylow", "DlcName": "mpchristmas2017", "ModificationsCount": 0}, {"Name": "WATER_refmap_poolside", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "WATER_refmap_silverlake", "DlcName": "mpchristmas2017", "ModificationsCount": 0}, {"Name": "WATER_refmap_venice", "DlcName": "mpchristmas2017", "ModificationsCount": 0}, {"Name": "FORdoron_delete", "DlcName": "mpchristmas2017", "ModificationsCount": 4}, {"Name": "NO_fog_alpha", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "V_strip_nofog", "DlcName": "mpchristmas2017", "ModificationsCount": 28}, {"Name": "METRO_Tunnels", "DlcName": "mpchristmas2017", "ModificationsCount": 59}, {"Name": "METRO_Tunnels_entrance", "DlcName": "mpchristmas2017", "ModificationsCount": 31}, {"Name": "METRO_platform", "DlcName": "mpchristmas2017", "ModificationsCount": 30}, {"Name": "STRIP_stage", "DlcName": "mpchristmas2017", "ModificationsCount": 55}, {"Name": "STRIP_office", "DlcName": "mpchristmas2017", "ModificationsCount": 30}, {"Name": "STRIP_changing", "DlcName": "mpchristmas2017", "ModificationsCount": 30}, {"Name": "INT_NO_fogALPHA", "DlcName": "mpchristmas2017", "ModificationsCount": 3}, {"Name": "STRIP_nofog", "DlcName": "mpchristmas2017", "ModificationsCount": 30}, {"Name": "INT_streetlighting", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "ch2_tunnel_whitelight", "DlcName": "mpchristmas2017", "ModificationsCount": 6}, {"Name": "AmbientPUSH", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "ship_lighting", "DlcName": "mpchristmas2017", "ModificationsCount": 4}, {"Name": "powerplant_nightlight", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "paleto_nightlight", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "militarybase_nightlight", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "sandyshore_nightlight", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "jewel_gas", "DlcName": "mpchristmas2017", "ModificationsCount": 0}, {"Name": "WATER_refmap_off", "DlcName": "mpchristmas2017", "ModificationsCount": 13}, {"Name": "trailer_explosion_optimise", "DlcName": "mpchristmas2017", "ModificationsCount": 19}, {"Name": "nervousRON_fog", "DlcName": "mpchristmas2017", "ModificationsCount": 7}, {"Name": "DONT_overide_sunpos", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "gallery_refmod", "DlcName": "mpchristmas2017", "ModificationsCount": 20}, {"Name": "prison_nightlight", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "multiplayer_ped_fight", "DlcName": "mpchristmas2017", "ModificationsCount": 4}, {"Name": "ship_explosion_underwater", "DlcName": "mpchristmas2017", "ModificationsCount": 20}, {"Name": "EXTRA_bouncelight", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "secret_camera", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "canyon_mission", "DlcName": "mpchristmas2017", "ModificationsCount": 23}, {"Name": "gorge_reflection_gpu", "DlcName": "mpchristmas2017", "ModificationsCount": 4}, {"Name": "subBASE_water_ref", "DlcName": "mpchristmas2017", "ModificationsCount": 13}, {"Name": "poolsidewaterreflection2", "DlcName": "mpchristmas2017", "ModificationsCount": 13}, {"Name": "CUSTOM_streetlight", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "ufo", "DlcName": "mpchristmas2017", "ModificationsCount": 32}, {"Name": "ship_explosion_underwater", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "lab_none_exit", "DlcName": "mpchristmas2017", "ModificationsCount": 24}, {"Name": "FinaleBankexit", "DlcName": "mpchristmas2017", "ModificationsCount": 28}, {"Name": "prologue_reflection_opt", "DlcName": "mpchristmas2017", "ModificationsCount": 4}, {"Name": "tunnel_entrance", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "tunnel_entrance_INT", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "id1_11_tunnel", "DlcName": "mpchristmas2017", "ModificationsCount": 31}, {"Name": "reflection_correct_ambient", "DlcName": "mpchristmas2017", "ModificationsCount": 3}, {"Name": "scanline_cam_cheap", "DlcName": "mpchristmas2017", "ModificationsCount": 12}, {"Name": "scanline_cam", "DlcName": "mpchristmas2017", "ModificationsCount": 16}, {"Name": "VC_tunnel_entrance", "DlcName": "mpchristmas2017", "ModificationsCount": 0}, {"Name": "WATER_REF_malibu", "DlcName": "mpchristmas2017", "ModificationsCount": 13}, {"Name": "carpark_dt1_02", "DlcName": "mpchristmas2017", "ModificationsCount": 31}, {"Name": "FIB_interview_optimise", "DlcName": "mpchristmas2017", "ModificationsCount": 21}, {"Name": "Prologue_shootout_opt", "DlcName": "mpchristmas2017", "ModificationsCount": 10}, {"Name": "hangar_lightsmod", "DlcName": "mpchristmas2017", "ModificationsCount": 2}, {"Name": "plane_inside_mode", "DlcName": "mpchristmas2017", "ModificationsCount": 34}, {"Name": "eatra_bouncelight_beach", "DlcName": "mpchristmas2017", "ModificationsCount": 5}, {"Name": "downtown_FIB_cascades_opt", "DlcName": "mpchristmas2017", "ModificationsCount": 3}, {"Name": "jewel_optim", "DlcName": "mpchristmas2017", "ModificationsCount": 2}, {"Name": "gorge_reflectionoffset2", "DlcName": "mpchristmas2017", "ModificationsCount": 2}, {"Name": "ufo_deathray", "DlcName": "mpchristmas2017", "ModificationsCount": 59}, {"Name": "PORT_heist_underwater", "DlcName": "mpchristmas2017", "ModificationsCount": 5}, {"Name": "TUNNEL_orange_exterior", "DlcName": "mpchristmas2017", "ModificationsCount": 17}, {"Name": "hillstunnel", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "jewelry_entrance_INT", "DlcName": "mpchristmas2017", "ModificationsCount": 2}, {"Name": "jewelry_entrance", "DlcName": "mpchristmas2017", "ModificationsCount": 2}, {"Name": "jewelry_entrance_INT_fog", "DlcName": "mpchristmas2017", "ModificationsCount": 2}, {"Name": "TUNNEL_yellow_ext", "DlcName": "mpchristmas2017", "ModificationsCount": 12}, {"Name": "NEW_jewel_EXIT", "DlcName": "mpchristmas2017", "ModificationsCount": 35}, {"Name": "services_nightlight", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "CS1_railwayB_tunnel", "DlcName": "mpchristmas2017", "ModificationsCount": 21}, {"Name": "TUNNEL_green_ext", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "CAMERA_secuirity", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "CAMERA_secuirity_FUZZ", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "int_hospital_small", "DlcName": "mpchristmas2017", "ModificationsCount": 26}, {"Name": "int_hospital_dark", "DlcName": "mpchristmas2017", "ModificationsCount": 26}, {"Name": "plaza_carpark", "DlcName": "mpchristmas2017", "ModificationsCount": 27}, {"Name": "gen_bank", "DlcName": "mpchristmas2017", "ModificationsCount": 27}, {"Name": "nightvision", "DlcName": "mpchristmas2017", "ModificationsCount": 30}, {"Name": "WATER_cove", "DlcName": "mpchristmas2017", "ModificationsCount": 116}, {"Name": "glasses_Darkblue", "DlcName": "mpchristmas2017", "ModificationsCount": 15}, {"Name": "glasses_VISOR", "DlcName": "mpchristmas2017", "ModificationsCount": 18}, {"Name": "heist_boat", "DlcName": "mpchristmas2017", "ModificationsCount": 15}, {"Name": "heist_boat_norain", "DlcName": "mpchristmas2017", "ModificationsCount": 16}, {"Name": "heist_boat_engineRoom", "DlcName": "mpchristmas2017", "ModificationsCount": 15}, {"Name": "buggy_shack", "DlcName": "mpchristmas2017", "ModificationsCount": 28}, {"Name": "mineshaft", "DlcName": "mpchristmas2017", "ModificationsCount": 29}, {"Name": "NG_first", "DlcName": "mpchristmas2017", "ModificationsCount": 11}, {"Name": "glasses_Scuba", "DlcName": "mpchristmas2017", "ModificationsCount": 9}, {"Name": "mugShot", "DlcName": "mpchristmas2017", "ModificationsCount": 53}, {"Name": "Glasses_BlackOut", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "winning_room", "DlcName": "mpchristmas2017", "ModificationsCount": 136}, {"Name": "mugShot_lineup", "DlcName": "mpchristmas2017", "ModificationsCount": 54}, {"Name": "MPApartHigh_palnning", "DlcName": "mpchristmas2017", "ModificationsCount": 56}, {"Name": "v_dark", "DlcName": "mpchristmas2017", "ModificationsCount": 67}, {"Name": "vehicle_subint", "DlcName": "mpchristmas2017", "ModificationsCount": 12}, {"Name": "Carpark_MP_exit", "DlcName": "mpchristmas2017", "ModificationsCount": 13}, {"Name": "EXT_FULLAmbientmult_art", "DlcName": "mpchristmas2017", "ModificationsCount": 1}, {"Name": "new_MP_Garage_L", "DlcName": "mpchristmas2017", "ModificationsCount": 45}, {"Name": "fp_vig_black", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "fp_vig_brown", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "fp_vig_gray", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "fp_vig_blue", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "fp_vig_red", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "fp_vig_green", "DlcName": "mpchristmas2017", "ModificationsCount": 8}, {"Name": "INT_trailer_cinema", "DlcName": "mpchristmas2017", "ModificationsCount": 30}, {"Name": "heliGunCam", "DlcName": "mpchristmas2017", "ModificationsCount": 20}, {"Name": "INT_smshop", "DlcName": "mpchristmas2017", "ModificationsCount": 38}, {"Name": "INT_mall", "DlcName": "mpchristmas2017", "ModificationsCount": 48}, {"Name": "Mp_<PERSON>ilts", "DlcName": "mpchristmas2017", "ModificationsCount": 29}, {"Name": "Mp_Stilts_gym", "DlcName": "mpchristmas2017", "ModificationsCount": 30}, {"Name": "Mp_Stilts2", "DlcName": "mpchristmas2017", "ModificationsCount": 29}, {"Name": "Mp_Stilts_gym2", "DlcName": "mpchristmas2017", "ModificationsCount": 30}, {"Name": "MPApart_H_01", "DlcName": "mpchristmas2017", "ModificationsCount": 53}, {"Name": "MPApart_H_01_gym", "DlcName": "mpchristmas2017", "ModificationsCount": 53}, {"Name": "MP_H_01_Study", "DlcName": "mpchristmas2017", "ModificationsCount": 53}, {"Name": "MP_H_01_Bedroom", "DlcName": "mpchristmas2017", "ModificationsCount": 53}, {"Name": "MP_H_01_Bathroom", "DlcName": "mpchristmas2017", "ModificationsCount": 53}, {"Name": "MP_H_01_New", "DlcName": "mpchristmas2017", "ModificationsCount": 52}, {"Name": "MP_H_01_New_Bedroom", "DlcName": "mpchristmas2017", "ModificationsCount": 53}, {"Name": "MP_H_01_New_Bathroom", "DlcName": "mpchristmas2017", "ModificationsCount": 53}, {"Name": "MP_H_01_New_Study", "DlcName": "mpchristmas2017", "ModificationsCount": 53}, {"Name": "INT_smshop_inMOD", "DlcName": "mpchristmas2017", "ModificationsCount": 24}, {"Name": "NoPedLight", "DlcName": "mpchristmas2017", "ModificationsCount": 4}, {"Name": "morgue_dark_ovr", "DlcName": "mpchristmas2017", "ModificationsCount": 45}, {"Name": "INT_smshop_outdoor_bloom", "DlcName": "mpchristmas2017", "ModificationsCount": 3}, {"Name": "INT_smshop_indoor_bloom", "DlcName": "mpchristmas2017", "ModificationsCount": 3}, {"Name": "MP_H_02", "DlcName": "mpchristmas2017", "ModificationsCount": 53}, {"Name": "MP_H_04", "DlcName": "mpchristmas2017", "ModificationsCount": 53}, {"Name": "Mp_Stilts2_bath", "DlcName": "mpchristmas2017", "ModificationsCount": 30}, {"Name": "mp_h_05", "DlcName": "mpchristmas2017", "ModificationsCount": 53}, {"Name": "mp_h_07", "DlcName": "mpchristmas2017", "ModificationsCount": 53}, {"Name": "MP_H_06", "DlcName": "mpchristmas2017", "ModificationsCount": 53}, {"Name": "mp_h_08", "DlcName": "mpchristmas2017", "ModificationsCount": 53}, {"Name": "yacht_DLC", "DlcName": "mpchristmas2017", "ModificationsCount": 20}, {"Name": "mp_exec_office_01", "DlcName": "mpchristmas2017", "ModificationsCount": 26}, {"Name": "mp_exec_warehouse_01", "DlcName": "mpchristmas2017", "ModificationsCount": 32}, {"Name": "mp_exec_office_02", "DlcName": "mpchristmas2017", "ModificationsCount": 27}, {"Name": "mp_exec_office_03", "DlcName": "mpchristmas2017", "ModificationsCount": 29}, {"Name": "mp_exec_office_04", "DlcName": "mpchristmas2017", "ModificationsCount": 27}, {"Name": "mp_exec_office_05", "DlcName": "mpchristmas2017", "ModificationsCount": 28}, {"Name": "mp_exec_office_06", "DlcName": "mpchristmas2017", "ModificationsCount": 28}, {"Name": "mp_exec_office_03_blue", "DlcName": "mpchristmas2017", "ModificationsCount": 32}, {"Name": "mp_exec_office_03C", "DlcName": "mpchristmas2017", "ModificationsCount": 32}, {"Name": "mp_bkr_int01_garage", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "mp_bkr_int01_transition", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "mp_bkr_int01_small_rooms", "DlcName": "mpchristmas2017", "ModificationsCount": 35}, {"Name": "mp_bkr_int02_garage", "DlcName": "mpchristmas2017", "ModificationsCount": 23}, {"Name": "mp_bkr_int02_hangout", "DlcName": "mpchristmas2017", "ModificationsCount": 24}, {"Name": "mp_bkr_int02_small_rooms", "DlcName": "mpchristmas2017", "ModificationsCount": 28}, {"Name": "mp_bkr_ware01", "DlcName": "mpchristmas2017", "ModificationsCount": 32}, {"Name": "mp_bkr_ware02_standard", "DlcName": "mpchristmas2017", "ModificationsCount": 29}, {"Name": "mp_bkr_ware02_upgrade", "DlcName": "mpchristmas2017", "ModificationsCount": 29}, {"Name": "mp_bkr_ware02_dry", "DlcName": "mpchristmas2017", "ModificationsCount": 22}, {"Name": "mp_bkr_ware03_basic", "DlcName": "mpchristmas2017", "ModificationsCount": 29}, {"Name": "mp_bkr_ware03_upgrade", "DlcName": "mpchristmas2017", "ModificationsCount": 28}, {"Name": "mp_bkr_ware04", "DlcName": "mpchristmas2017", "ModificationsCount": 25}, {"Name": "mp_bkr_ware05", "DlcName": "mpchristmas2017", "ModificationsCount": 30}, {"Name": "mp_lad_night", "DlcName": "mpchristmas2017", "ModificationsCount": 7}, {"Name": "mp_lad_day", "DlcName": "mpchristmas2017", "ModificationsCount": 7}, {"Name": "mp_lad_judgment", "DlcName": "mpchristmas2017", "ModificationsCount": 7}, {"Name": "mp_imx_intwaremed", "DlcName": "mpchristmas2017", "ModificationsCount": 41}, {"Name": "mp_imx_intwaremed_office", "DlcName": "mpchristmas2017", "ModificationsCount": 41}, {"Name": "mp_imx_mod_int_01", "DlcName": "mpchristmas2017", "ModificationsCount": 30}, {"Name": "IMpExt_Interior_02", "DlcName": "mpchristmas2017", "ModificationsCount": 57}, {"Name": "ImpExp_Interior_01", "DlcName": "mpchristmas2017", "ModificationsCount": 55}, {"Name": "impexp_interior_01_lift", "DlcName": "mpchristmas2017", "ModificationsCount": 56}, {"Name": "IMpExt_Interior_02_stair_cage", "DlcName": "mpchristmas2017", "ModificationsCount": 57}, {"Name": "mp_gr_int01_white", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "mp_gr_int01_grey", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "mp_gr_int01_black", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "grdlc_int_02", "DlcName": "mpchristmas2017", "ModificationsCount": 34}, {"Name": "mp_nightshark_shield_fp", "DlcName": "mpchristmas2017", "ModificationsCount": 4}, {"Name": "grdlc_int_02_trailer_cave", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "mp_smg_int01_han", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "mp_smg_int01_han_red", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "mp_smg_int01_han_blue", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "mp_smg_int01_han_yellow", "DlcName": "mpchristmas2017", "ModificationsCount": 33}, {"Name": "mp_x17dlc_in_sub", "DlcName": "mpchristmas2017", "ModificationsCount": 37}, {"Name": "mp_x17dlc_in_sub_no_reflection", "DlcName": "mpchristmas2017", "ModificationsCount": 36}, {"Name": "mp_x17dlc_base", "DlcName": "mpchristmas2017", "ModificationsCount": 37}, {"Name": "mp_x17dlc_base_dark", "DlcName": "mpchristmas2017", "ModificationsCount": 37}, {"Name": "mp_x17dlc_base_darkest", "DlcName": "mpchristmas2017", "ModificationsCount": 37}, {"Name": "mp_x17dlc_lab", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "mp_x17dlc_lab_loading_bay", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "mp_x17dlc_facility", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "mp_x17dlc_facility_conference", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "mp_x17dlc_int_01", "DlcName": "mpchristmas2017", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint1", "DlcName": "mpchristmas2017", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint2", "DlcName": "mpchristmas2017", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint3", "DlcName": "mpchristmas2017", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint4", "DlcName": "mpchristmas2017", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint5", "DlcName": "mpchristmas2017", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint6", "DlcName": "mpchristmas2017", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint7", "DlcName": "mpchristmas2017", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint8", "DlcName": "mpchristmas2017", "ModificationsCount": 49}, {"Name": "mp_x17dlc_int_01_tint9", "DlcName": "mpchristmas2017", "ModificationsCount": 49}, {"Name": "mp_x17dlc_facility2", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "mp_x17dlc_int_02", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "mp_x17dlc_int_02_tint1", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "mp_x17dlc_int_02_tint2", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "mp_x17dlc_int_02_tint3", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "mp_x17dlc_int_02_tint4", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "mp_x17dlc_int_02_tint5", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "mp_x17dlc_int_02_tint6", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "mp_x17dlc_int_02_tint7", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "mp_x17dlc_int_02_tint8", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "mp_x17dlc_int_02_tint9", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "mp_x17dlc_int_02_hangar", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "mp_x17dlc_int_silo", "DlcName": "mpchristmas2017", "ModificationsCount": 47}, {"Name": "mp_x17dlc_int_silo_escape", "DlcName": "mpchristmas2017", "ModificationsCount": 48}, {"Name": "li", "DlcName": "mpgunrunning", "ModificationsCount": 32}, {"Name": "underwater", "DlcName": "mpgunrunning", "ModificationsCount": 115}, {"Name": "underwater_deep", "DlcName": "mpgunrunning", "ModificationsCount": 81}, {"Name": "NoAmbientmult", "DlcName": "mpgunrunning", "ModificationsCount": 4}, {"Name": "superDARK", "DlcName": "mpgunrunning", "ModificationsCount": 3}, {"Name": "CAMERA_BW", "DlcName": "mpgunrunning", "ModificationsCount": 33}, {"Name": "Forest", "DlcName": "mpgunrunning", "ModificationsCount": 30}, {"Name": "micheal", "DlcName": "mpgunrunning", "ModificationsCount": 10}, {"Name": "TREVOR", "DlcName": "mpgunrunning", "ModificationsCount": 15}, {"Name": "FRANKLIN", "DlcName": "mpgunrunning", "ModificationsCount": 20}, {"Name": "Tunnel", "DlcName": "mpgunrunning", "ModificationsCount": 13}, {"Name": "carpark", "DlcName": "mpgunrunning", "ModificationsCount": 18}, {"Name": "NEW_abattoir", "DlcName": "mpgunrunning", "ModificationsCount": 67}, {"Name": "Vagos", "DlcName": "mpgunrunning", "ModificationsCount": 23}, {"Name": "cops", "DlcName": "mpgunrunning", "ModificationsCount": 18}, {"Name": "Bikers", "DlcName": "mpgunrunning", "ModificationsCount": 28}, {"Name": "BikersSPLASH", "DlcName": "mpgunrunning", "ModificationsCount": 30}, {"Name": "VagosSPLASH", "DlcName": "mpgunrunning", "ModificationsCount": 21}, {"Name": "CopsSPLASH", "DlcName": "mpgunrunning", "ModificationsCount": 22}, {"Name": "VAGOS_new_garage", "DlcName": "mpgunrunning", "ModificationsCount": 24}, {"Name": "VAGOS_new_hangout", "DlcName": "mpgunrunning", "ModificationsCount": 26}, {"Name": "NEW_jewel", "DlcName": "mpgunrunning", "ModificationsCount": 35}, {"Name": "frankilnsAUNTS_new", "DlcName": "mpgunrunning", "ModificationsCount": 37}, {"Name": "frankilnsAUNTS_SUNdir", "DlcName": "mpgunrunning", "ModificationsCount": 2}, {"Name": "StreetLighting", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "NEW_tunnels", "DlcName": "mpgunrunning", "ModificationsCount": 26}, {"Name": "NEW_yellowtunnels", "DlcName": "mpgunrunning", "ModificationsCount": 55}, {"Name": "NEW_tunnels_hole", "DlcName": "mpgunrunning", "ModificationsCount": 31}, {"Name": "NEW_tunnels_ditch", "DlcName": "mpgunrunning", "ModificationsCount": 30}, {"Name": "Paleto", "DlcName": "mpgunrunning", "ModificationsCount": 6}, {"Name": "new_bank", "DlcName": "mpgunrunning", "ModificationsCount": 17}, {"Name": "ReduceDrawDistance", "DlcName": "mpgunrunning", "ModificationsCount": 10}, {"Name": "ReduceDrawDistanceMission", "DlcName": "mpgunrunning", "ModificationsCount": 6}, {"Name": "lightpolution", "DlcName": "mpgunrunning", "ModificationsCount": 16}, {"Name": "NEW_lesters", "DlcName": "mpgunrunning", "ModificationsCount": 35}, {"Name": "ReduceDrawDistanceMAP", "DlcName": "mpgunrunning", "ModificationsCount": 6}, {"Name": "reducewaterREF", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "garage", "DlcName": "mpgunrunning", "ModificationsCount": 12}, {"Name": "LightPollutionHills", "DlcName": "mpgunrunning", "ModificationsCount": 16}, {"Name": "NewMicheal", "DlcName": "mpgunrunning", "ModificationsCount": 30}, {"Name": "NewMichealupstairs", "DlcName": "mpgunrunning", "ModificationsCount": 20}, {"Name": "NewMichealstoilet", "DlcName": "mpgunrunning", "ModificationsCount": 18}, {"Name": "NewMichealgirly", "DlcName": "mpgunrunning", "ModificationsCount": 29}, {"Name": "WATER_port", "DlcName": "mpgunrunning", "ModificationsCount": 60}, {"Name": "WATER_salton", "DlcName": "mpgunrunning", "ModificationsCount": 68}, {"Name": "WATER_river", "DlcName": "mpgunrunning", "ModificationsCount": 61}, {"Name": "FIB_interview", "DlcName": "mpgunrunning", "ModificationsCount": 33}, {"Name": "NEW_station_unfinished", "DlcName": "mpgunrunning", "ModificationsCount": 33}, {"Name": "cashdepot", "DlcName": "mpgunrunning", "ModificationsCount": 30}, {"Name": "cashdepotEMERGENCY", "DlcName": "mpgunrunning", "ModificationsCount": 36}, {"Name": "FrankilinsHOUSEhills", "DlcName": "mpgunrunning", "ModificationsCount": 29}, {"Name": "HicksbarNEW", "DlcName": "mpgunrunning", "ModificationsCount": 32}, {"Name": "NOdirectLight", "DlcName": "mpgunrunning", "ModificationsCount": 0}, {"Name": "SALTONSEA", "DlcName": "mpgunrunning", "ModificationsCount": 59}, {"Name": "TUNNEL_green", "DlcName": "mpgunrunning", "ModificationsCount": 31}, {"Name": "NewMicheal_night", "DlcName": "mpgunrunning", "ModificationsCount": 24}, {"Name": "WATER_muddy", "DlcName": "mpgunrunning", "ModificationsCount": 69}, {"Name": "WATER_shore", "DlcName": "mpgunrunning", "ModificationsCount": 22}, {"Name": "damage", "DlcName": "mpgunrunning", "ModificationsCount": 11}, {"Name": "hitped", "DlcName": "mpgunrunning", "ModificationsCount": 2}, {"Name": "dying", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "overwater", "DlcName": "mpgunrunning", "ModificationsCount": 6}, {"Name": "whitenightlighting", "DlcName": "mpgunrunning", "ModificationsCount": 6}, {"Name": "TUNNEL_yellow", "DlcName": "mpgunrunning", "ModificationsCount": 32}, {"Name": "buildingTOP", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "WATER_lab", "DlcName": "mpgunrunning", "ModificationsCount": 89}, {"Name": "cinema", "DlcName": "mpgunrunning", "ModificationsCount": 36}, {"Name": "fireDEPT", "DlcName": "mpgunrunning", "ModificationsCount": 34}, {"Name": "ranch", "DlcName": "mpgunrunning", "ModificationsCount": 33}, {"Name": "TUNNEL_white", "DlcName": "mpgunrunning", "ModificationsCount": 36}, {"Name": "V_recycle_mainroom", "DlcName": "mpgunrunning", "ModificationsCount": 26}, {"Name": "V_recycle_dark", "DlcName": "mpgunrunning", "ModificationsCount": 27}, {"Name": "V_recycle_light", "DlcName": "mpgunrunning", "ModificationsCount": 26}, {"Name": "lightning_weak", "DlcName": "mpgunrunning", "ModificationsCount": 33}, {"Name": "lightning_strong", "DlcName": "mpgunrunning", "ModificationsCount": 36}, {"Name": "lightning_cloud", "DlcName": "mpgunrunning", "ModificationsCount": 9}, {"Name": "gunclubrange", "DlcName": "mpgunrunning", "ModificationsCount": 36}, {"Name": "NoAmbientmult_interior", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "FullAmbientmult_interior", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "StreetLightingJunction", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "StreetLightingtraffic", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "Multipayer_spectatorCam", "DlcName": "mpgunrunning", "ModificationsCount": 18}, {"Name": "INT_NoAmbientmult", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "INT_NoAmbientmult_art", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "INT_FullAmbientmult", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "INT_FULLAmbientmult_art", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "INT_FULLAmbientmult_both", "DlcName": "mpgunrunning", "ModificationsCount": 2}, {"Name": "INT_NoAmbientmult_both", "DlcName": "mpgunrunning", "ModificationsCount": 2}, {"Name": "<PERSON><PERSON><PERSON>", "DlcName": "mpgunrunning", "ModificationsCount": 20}, {"Name": "ReduceSSAO", "DlcName": "mpgunrunning", "ModificationsCount": 0}, {"Name": "scope_zoom_in", "DlcName": "mpgunrunning", "ModificationsCount": 5}, {"Name": "scope_zoom_out", "DlcName": "mpgunrunning", "ModificationsCount": 5}, {"Name": "crane_cam", "DlcName": "mpgunrunning", "ModificationsCount": 19}, {"Name": "WATER_silty", "DlcName": "mpgunrunning", "ModificationsCount": 64}, {"Name": "Trevors_room", "DlcName": "mpgunrunning", "ModificationsCount": 31}, {"Name": "Hint_cam", "DlcName": "mpgunrunning", "ModificationsCount": 16}, {"Name": "venice_canal_tunnel", "DlcName": "mpgunrunning", "ModificationsCount": 47}, {"Name": "blackNwhite", "DlcName": "mpgunrunning", "ModificationsCount": 22}, {"Name": "projector", "DlcName": "mpgunrunning", "ModificationsCount": 12}, {"Name": "paleto_opt", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "WAREHOUSE", "DlcName": "mpgunrunning", "ModificationsCount": 27}, {"Name": "pulse", "DlcName": "mpgunrunning", "ModificationsCount": 20}, {"Name": "sleeping", "DlcName": "mpgunrunning", "ModificationsCount": 10}, {"Name": "INT_garage", "DlcName": "mpgunrunning", "ModificationsCount": 19}, {"Name": "nextgen", "DlcName": "mpgunrunning", "ModificationsCount": 5}, {"Name": "crane_cam_cinematic", "DlcName": "mpgunrunning", "ModificationsCount": 7}, {"Name": "TUNNEL_orange", "DlcName": "mpgunrunning", "ModificationsCount": 33}, {"Name": "traffic_skycam", "DlcName": "mpgunrunning", "ModificationsCount": 30}, {"Name": "powerstation", "DlcName": "mpgunrunning", "ModificationsCount": 7}, {"Name": "SAWMILL", "DlcName": "mpgunrunning", "ModificationsCount": 9}, {"Name": "LODmult_global_reduce", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "LODmult_HD_orphan_reduce", "DlcName": "mpgunrunning", "ModificationsCount": 2}, {"Name": "LODmult_HD_orphan_LOD_reduce", "DlcName": "mpgunrunning", "ModificationsCount": 3}, {"Name": "LODmult_LOD_reduce", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "LODmult_SLOD1_reduce", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "LODmult_SLOD2_reduce", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "LODmult_SLOD3_reduce", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "NewMicheal_upstairs", "DlcName": "mpgunrunning", "ModificationsCount": 24}, {"Name": "micheals_lightsOFF", "DlcName": "mpgunrunning", "ModificationsCount": 2}, {"Name": "telescope", "DlcName": "mpgunrunning", "ModificationsCount": 25}, {"Name": "WATER_silverlake", "DlcName": "mpgunrunning", "ModificationsCount": 56}, {"Name": "WATER _lab_cooling", "DlcName": "mpgunrunning", "ModificationsCount": 95}, {"Name": "baseTONEMAPPING", "DlcName": "mpgunrunning", "ModificationsCount": 16}, {"Name": "WATER_salton_bottom", "DlcName": "mpgunrunning", "ModificationsCount": 37}, {"Name": "new_stripper_changing", "DlcName": "mpgunrunning", "ModificationsCount": 29}, {"Name": "underwater_deep_clear", "DlcName": "mpgunrunning", "ModificationsCount": 4}, {"Name": "prologue_ending_fog", "DlcName": "mpgunrunning", "ModificationsCount": 25}, {"Name": "graveyard_shootout", "DlcName": "mpgunrunning", "ModificationsCount": 16}, {"Name": "morebloom", "DlcName": "mpgunrunning", "ModificationsCount": 3}, {"Name": "LIGHTSreduceFALLOFF", "DlcName": "mpgunrunning", "ModificationsCount": 0}, {"Name": "INT_posh_hairdresser", "DlcName": "mpgunrunning", "ModificationsCount": 23}, {"Name": "V_strip_office", "DlcName": "mpgunrunning", "ModificationsCount": 28}, {"Name": "sunglasses", "DlcName": "mpgunrunning", "ModificationsCount": 15}, {"Name": "vespucci_garage", "DlcName": "mpgunrunning", "ModificationsCount": 34}, {"Name": "half_direct", "DlcName": "mpgunrunning", "ModificationsCount": 3}, {"Name": "carpark_dt1_03", "DlcName": "mpgunrunning", "ModificationsCount": 20}, {"Name": "tunnel_id1_11", "DlcName": "mpgunrunning", "ModificationsCount": 20}, {"Name": "reducelightingcost", "DlcName": "mpgunrunning", "ModificationsCount": 2}, {"Name": "NOrain", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "morgue_dark", "DlcName": "mpgunrunning", "ModificationsCount": 44}, {"Name": "CS3_rail_tunnel", "DlcName": "mpgunrunning", "ModificationsCount": 19}, {"Name": "new_tunnels_entrance", "DlcName": "mpgunrunning", "ModificationsCount": 47}, {"Name": "spectator1", "DlcName": "mpgunrunning", "ModificationsCount": 41}, {"Name": "spectator2", "DlcName": "mpgunrunning", "ModificationsCount": 34}, {"Name": "spectator3", "DlcName": "mpgunrunning", "ModificationsCount": 33}, {"Name": "spectator4", "DlcName": "mpgunrunning", "ModificationsCount": 36}, {"Name": "spectator5", "DlcName": "mpgunrunning", "ModificationsCount": 63}, {"Name": "spectator6", "DlcName": "mpgunrunning", "ModificationsCount": 43}, {"Name": "spectator7", "DlcName": "mpgunrunning", "ModificationsCount": 48}, {"Name": "spectator8", "DlcName": "mpgunrunning", "ModificationsCount": 37}, {"Name": "spectator9", "DlcName": "mpgunrunning", "ModificationsCount": 53}, {"Name": "spectator10", "DlcName": "mpgunrunning", "ModificationsCount": 31}, {"Name": "INT_NOdirectLight", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "WATER_resevoir", "DlcName": "mpgunrunning", "ModificationsCount": 68}, {"Name": "WATER_hills", "DlcName": "mpgunrunning", "ModificationsCount": 66}, {"Name": "WATER_militaryPOOP", "DlcName": "mpgunrunning", "ModificationsCount": 62}, {"Name": "NEW_ornate_bank", "DlcName": "mpgunrunning", "ModificationsCount": 43}, {"Name": "NEW_ornate_bank_safe", "DlcName": "mpgunrunning", "ModificationsCount": 46}, {"Name": "NEW_ornate_bank_entrance", "DlcName": "mpgunrunning", "ModificationsCount": 38}, {"Name": "NEW_ornate_bank_office", "DlcName": "mpgunrunning", "ModificationsCount": 46}, {"Name": "LODmult_global_reduce_NOHD", "DlcName": "mpgunrunning", "ModificationsCount": 6}, {"Name": "interior_WATER_lighting", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "gorge_reflectionoffset", "DlcName": "mpgunrunning", "ModificationsCount": 2}, {"Name": "eyeINtheSKY", "DlcName": "mpgunrunning", "ModificationsCount": 17}, {"Name": "resvoire_reflection", "DlcName": "mpgunrunning", "ModificationsCount": 6}, {"Name": "NO_weather", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "prologue_ext_art_amb", "DlcName": "mpgunrunning", "ModificationsCount": 12}, {"Name": "prologue_shootout", "DlcName": "mpgunrunning", "ModificationsCount": 20}, {"Name": "heathaze", "DlcName": "mpgunrunning", "ModificationsCount": 22}, {"Name": "KT_underpass", "DlcName": "mpgunrunning", "ModificationsCount": 25}, {"Name": "INT_nowaterREF", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "carMOD_underpass", "DlcName": "mpgunrunning", "ModificationsCount": 28}, {"Name": "refit", "DlcName": "mpgunrunning", "ModificationsCount": 34}, {"Name": "NO_streetAmbient", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "NO_coronas", "DlcName": "mpgunrunning", "ModificationsCount": 2}, {"Name": "epsilion", "DlcName": "mpgunrunning", "ModificationsCount": 24}, {"Name": "WATER_refmap_high", "DlcName": "mpgunrunning", "ModificationsCount": 0}, {"Name": "WATER_refmap_med", "DlcName": "mpgunrunning", "ModificationsCount": 0}, {"Name": "WATER_refmap_low", "DlcName": "mpgunrunning", "ModificationsCount": 0}, {"Name": "WATER_refmap_verylow", "DlcName": "mpgunrunning", "ModificationsCount": 0}, {"Name": "WATER_refmap_poolside", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "WATER_refmap_silverlake", "DlcName": "mpgunrunning", "ModificationsCount": 0}, {"Name": "WATER_refmap_venice", "DlcName": "mpgunrunning", "ModificationsCount": 0}, {"Name": "FORdoron_delete", "DlcName": "mpgunrunning", "ModificationsCount": 4}, {"Name": "NO_fog_alpha", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "V_strip_nofog", "DlcName": "mpgunrunning", "ModificationsCount": 28}, {"Name": "METRO_Tunnels", "DlcName": "mpgunrunning", "ModificationsCount": 59}, {"Name": "METRO_Tunnels_entrance", "DlcName": "mpgunrunning", "ModificationsCount": 31}, {"Name": "METRO_platform", "DlcName": "mpgunrunning", "ModificationsCount": 30}, {"Name": "STRIP_stage", "DlcName": "mpgunrunning", "ModificationsCount": 55}, {"Name": "STRIP_office", "DlcName": "mpgunrunning", "ModificationsCount": 30}, {"Name": "STRIP_changing", "DlcName": "mpgunrunning", "ModificationsCount": 30}, {"Name": "INT_NO_fogALPHA", "DlcName": "mpgunrunning", "ModificationsCount": 3}, {"Name": "STRIP_nofog", "DlcName": "mpgunrunning", "ModificationsCount": 30}, {"Name": "INT_streetlighting", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "ch2_tunnel_whitelight", "DlcName": "mpgunrunning", "ModificationsCount": 6}, {"Name": "AmbientPUSH", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "ship_lighting", "DlcName": "mpgunrunning", "ModificationsCount": 4}, {"Name": "powerplant_nightlight", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "paleto_nightlight", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "militarybase_nightlight", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "sandyshore_nightlight", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "jewel_gas", "DlcName": "mpgunrunning", "ModificationsCount": 0}, {"Name": "WATER_refmap_off", "DlcName": "mpgunrunning", "ModificationsCount": 13}, {"Name": "trailer_explosion_optimise", "DlcName": "mpgunrunning", "ModificationsCount": 19}, {"Name": "nervousRON_fog", "DlcName": "mpgunrunning", "ModificationsCount": 7}, {"Name": "DONT_overide_sunpos", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "gallery_refmod", "DlcName": "mpgunrunning", "ModificationsCount": 20}, {"Name": "prison_nightlight", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "multiplayer_ped_fight", "DlcName": "mpgunrunning", "ModificationsCount": 4}, {"Name": "ship_explosion_underwater", "DlcName": "mpgunrunning", "ModificationsCount": 20}, {"Name": "EXTRA_bouncelight", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "secret_camera", "DlcName": "mpgunrunning", "ModificationsCount": 33}, {"Name": "canyon_mission", "DlcName": "mpgunrunning", "ModificationsCount": 23}, {"Name": "gorge_reflection_gpu", "DlcName": "mpgunrunning", "ModificationsCount": 4}, {"Name": "subBASE_water_ref", "DlcName": "mpgunrunning", "ModificationsCount": 13}, {"Name": "poolsidewaterreflection2", "DlcName": "mpgunrunning", "ModificationsCount": 13}, {"Name": "CUSTOM_streetlight", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "ufo", "DlcName": "mpgunrunning", "ModificationsCount": 32}, {"Name": "ship_explosion_underwater", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "lab_none_exit", "DlcName": "mpgunrunning", "ModificationsCount": 24}, {"Name": "FinaleBankexit", "DlcName": "mpgunrunning", "ModificationsCount": 28}, {"Name": "prologue_reflection_opt", "DlcName": "mpgunrunning", "ModificationsCount": 4}, {"Name": "tunnel_entrance", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "tunnel_entrance_INT", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "id1_11_tunnel", "DlcName": "mpgunrunning", "ModificationsCount": 31}, {"Name": "reflection_correct_ambient", "DlcName": "mpgunrunning", "ModificationsCount": 3}, {"Name": "scanline_cam_cheap", "DlcName": "mpgunrunning", "ModificationsCount": 12}, {"Name": "scanline_cam", "DlcName": "mpgunrunning", "ModificationsCount": 16}, {"Name": "VC_tunnel_entrance", "DlcName": "mpgunrunning", "ModificationsCount": 0}, {"Name": "WATER_REF_malibu", "DlcName": "mpgunrunning", "ModificationsCount": 13}, {"Name": "carpark_dt1_02", "DlcName": "mpgunrunning", "ModificationsCount": 31}, {"Name": "FIB_interview_optimise", "DlcName": "mpgunrunning", "ModificationsCount": 21}, {"Name": "Prologue_shootout_opt", "DlcName": "mpgunrunning", "ModificationsCount": 10}, {"Name": "hangar_lightsmod", "DlcName": "mpgunrunning", "ModificationsCount": 2}, {"Name": "plane_inside_mode", "DlcName": "mpgunrunning", "ModificationsCount": 34}, {"Name": "eatra_bouncelight_beach", "DlcName": "mpgunrunning", "ModificationsCount": 5}, {"Name": "downtown_FIB_cascades_opt", "DlcName": "mpgunrunning", "ModificationsCount": 3}, {"Name": "jewel_optim", "DlcName": "mpgunrunning", "ModificationsCount": 2}, {"Name": "gorge_reflectionoffset2", "DlcName": "mpgunrunning", "ModificationsCount": 2}, {"Name": "ufo_deathray", "DlcName": "mpgunrunning", "ModificationsCount": 59}, {"Name": "PORT_heist_underwater", "DlcName": "mpgunrunning", "ModificationsCount": 5}, {"Name": "TUNNEL_orange_exterior", "DlcName": "mpgunrunning", "ModificationsCount": 17}, {"Name": "hillstunnel", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "jewelry_entrance_INT", "DlcName": "mpgunrunning", "ModificationsCount": 2}, {"Name": "jewelry_entrance", "DlcName": "mpgunrunning", "ModificationsCount": 2}, {"Name": "jewelry_entrance_INT_fog", "DlcName": "mpgunrunning", "ModificationsCount": 2}, {"Name": "TUNNEL_yellow_ext", "DlcName": "mpgunrunning", "ModificationsCount": 12}, {"Name": "NEW_jewel_EXIT", "DlcName": "mpgunrunning", "ModificationsCount": 35}, {"Name": "services_nightlight", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "CS1_railwayB_tunnel", "DlcName": "mpgunrunning", "ModificationsCount": 21}, {"Name": "TUNNEL_green_ext", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "CAMERA_secuirity", "DlcName": "mpgunrunning", "ModificationsCount": 33}, {"Name": "CAMERA_secuirity_FUZZ", "DlcName": "mpgunrunning", "ModificationsCount": 33}, {"Name": "int_hospital_small", "DlcName": "mpgunrunning", "ModificationsCount": 26}, {"Name": "int_hospital_dark", "DlcName": "mpgunrunning", "ModificationsCount": 26}, {"Name": "plaza_carpark", "DlcName": "mpgunrunning", "ModificationsCount": 27}, {"Name": "gen_bank", "DlcName": "mpgunrunning", "ModificationsCount": 27}, {"Name": "nightvision", "DlcName": "mpgunrunning", "ModificationsCount": 30}, {"Name": "WATER_cove", "DlcName": "mpgunrunning", "ModificationsCount": 116}, {"Name": "glasses_Darkblue", "DlcName": "mpgunrunning", "ModificationsCount": 15}, {"Name": "glasses_VISOR", "DlcName": "mpgunrunning", "ModificationsCount": 18}, {"Name": "heist_boat", "DlcName": "mpgunrunning", "ModificationsCount": 15}, {"Name": "heist_boat_norain", "DlcName": "mpgunrunning", "ModificationsCount": 16}, {"Name": "heist_boat_engineRoom", "DlcName": "mpgunrunning", "ModificationsCount": 15}, {"Name": "buggy_shack", "DlcName": "mpgunrunning", "ModificationsCount": 28}, {"Name": "mineshaft", "DlcName": "mpgunrunning", "ModificationsCount": 29}, {"Name": "NG_first", "DlcName": "mpgunrunning", "ModificationsCount": 11}, {"Name": "glasses_Scuba", "DlcName": "mpgunrunning", "ModificationsCount": 9}, {"Name": "mugShot", "DlcName": "mpgunrunning", "ModificationsCount": 53}, {"Name": "Glasses_BlackOut", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "winning_room", "DlcName": "mpgunrunning", "ModificationsCount": 136}, {"Name": "mugShot_lineup", "DlcName": "mpgunrunning", "ModificationsCount": 54}, {"Name": "MPApartHigh_palnning", "DlcName": "mpgunrunning", "ModificationsCount": 56}, {"Name": "v_dark", "DlcName": "mpgunrunning", "ModificationsCount": 67}, {"Name": "vehicle_subint", "DlcName": "mpgunrunning", "ModificationsCount": 12}, {"Name": "Carpark_MP_exit", "DlcName": "mpgunrunning", "ModificationsCount": 13}, {"Name": "EXT_FULLAmbientmult_art", "DlcName": "mpgunrunning", "ModificationsCount": 1}, {"Name": "new_MP_Garage_L", "DlcName": "mpgunrunning", "ModificationsCount": 45}, {"Name": "fp_vig_black", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "fp_vig_brown", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "fp_vig_gray", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "fp_vig_blue", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "fp_vig_red", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "fp_vig_green", "DlcName": "mpgunrunning", "ModificationsCount": 8}, {"Name": "INT_trailer_cinema", "DlcName": "mpgunrunning", "ModificationsCount": 30}, {"Name": "heliGunCam", "DlcName": "mpgunrunning", "ModificationsCount": 20}, {"Name": "INT_smshop", "DlcName": "mpgunrunning", "ModificationsCount": 38}, {"Name": "INT_mall", "DlcName": "mpgunrunning", "ModificationsCount": 48}, {"Name": "Mp_<PERSON>ilts", "DlcName": "mpgunrunning", "ModificationsCount": 29}, {"Name": "Mp_Stilts_gym", "DlcName": "mpgunrunning", "ModificationsCount": 30}, {"Name": "Mp_Stilts2", "DlcName": "mpgunrunning", "ModificationsCount": 29}, {"Name": "Mp_Stilts_gym2", "DlcName": "mpgunrunning", "ModificationsCount": 30}, {"Name": "MPApart_H_01", "DlcName": "mpgunrunning", "ModificationsCount": 53}, {"Name": "MPApart_H_01_gym", "DlcName": "mpgunrunning", "ModificationsCount": 53}, {"Name": "MP_H_01_Study", "DlcName": "mpgunrunning", "ModificationsCount": 53}, {"Name": "MP_H_01_Bedroom", "DlcName": "mpgunrunning", "ModificationsCount": 53}, {"Name": "MP_H_01_Bathroom", "DlcName": "mpgunrunning", "ModificationsCount": 53}, {"Name": "MP_H_01_New", "DlcName": "mpgunrunning", "ModificationsCount": 52}, {"Name": "MP_H_01_New_Bedroom", "DlcName": "mpgunrunning", "ModificationsCount": 53}, {"Name": "MP_H_01_New_Bathroom", "DlcName": "mpgunrunning", "ModificationsCount": 53}, {"Name": "MP_H_01_New_Study", "DlcName": "mpgunrunning", "ModificationsCount": 53}, {"Name": "INT_smshop_inMOD", "DlcName": "mpgunrunning", "ModificationsCount": 24}, {"Name": "NoPedLight", "DlcName": "mpgunrunning", "ModificationsCount": 4}, {"Name": "morgue_dark_ovr", "DlcName": "mpgunrunning", "ModificationsCount": 45}, {"Name": "INT_smshop_outdoor_bloom", "DlcName": "mpgunrunning", "ModificationsCount": 3}, {"Name": "INT_smshop_indoor_bloom", "DlcName": "mpgunrunning", "ModificationsCount": 3}, {"Name": "MP_H_02", "DlcName": "mpgunrunning", "ModificationsCount": 53}, {"Name": "MP_H_04", "DlcName": "mpgunrunning", "ModificationsCount": 53}, {"Name": "Mp_Stilts2_bath", "DlcName": "mpgunrunning", "ModificationsCount": 30}, {"Name": "mp_h_05", "DlcName": "mpgunrunning", "ModificationsCount": 53}, {"Name": "mp_h_07", "DlcName": "mpgunrunning", "ModificationsCount": 53}, {"Name": "MP_H_06", "DlcName": "mpgunrunning", "ModificationsCount": 53}, {"Name": "mp_h_08", "DlcName": "mpgunrunning", "ModificationsCount": 53}, {"Name": "yacht_DLC", "DlcName": "mpgunrunning", "ModificationsCount": 20}, {"Name": "mp_exec_office_01", "DlcName": "mpgunrunning", "ModificationsCount": 26}, {"Name": "mp_exec_warehouse_01", "DlcName": "mpgunrunning", "ModificationsCount": 32}, {"Name": "mp_exec_office_02", "DlcName": "mpgunrunning", "ModificationsCount": 27}, {"Name": "mp_exec_office_03", "DlcName": "mpgunrunning", "ModificationsCount": 29}, {"Name": "mp_exec_office_04", "DlcName": "mpgunrunning", "ModificationsCount": 27}, {"Name": "mp_exec_office_05", "DlcName": "mpgunrunning", "ModificationsCount": 28}, {"Name": "mp_exec_office_06", "DlcName": "mpgunrunning", "ModificationsCount": 28}, {"Name": "mp_exec_office_03_blue", "DlcName": "mpgunrunning", "ModificationsCount": 32}, {"Name": "mp_exec_office_03C", "DlcName": "mpgunrunning", "ModificationsCount": 32}, {"Name": "mp_bkr_int01_garage", "DlcName": "mpgunrunning", "ModificationsCount": 33}, {"Name": "mp_bkr_int01_transition", "DlcName": "mpgunrunning", "ModificationsCount": 33}, {"Name": "mp_bkr_int01_small_rooms", "DlcName": "mpgunrunning", "ModificationsCount": 35}, {"Name": "mp_bkr_int02_garage", "DlcName": "mpgunrunning", "ModificationsCount": 23}, {"Name": "mp_bkr_int02_hangout", "DlcName": "mpgunrunning", "ModificationsCount": 24}, {"Name": "mp_bkr_int02_small_rooms", "DlcName": "mpgunrunning", "ModificationsCount": 28}, {"Name": "mp_bkr_ware01", "DlcName": "mpgunrunning", "ModificationsCount": 32}, {"Name": "mp_bkr_ware02_standard", "DlcName": "mpgunrunning", "ModificationsCount": 29}, {"Name": "mp_bkr_ware02_upgrade", "DlcName": "mpgunrunning", "ModificationsCount": 29}, {"Name": "mp_bkr_ware02_dry", "DlcName": "mpgunrunning", "ModificationsCount": 22}, {"Name": "mp_bkr_ware03_basic", "DlcName": "mpgunrunning", "ModificationsCount": 29}, {"Name": "mp_bkr_ware03_upgrade", "DlcName": "mpgunrunning", "ModificationsCount": 28}, {"Name": "mp_bkr_ware04", "DlcName": "mpgunrunning", "ModificationsCount": 25}, {"Name": "mp_bkr_ware05", "DlcName": "mpgunrunning", "ModificationsCount": 30}, {"Name": "mp_lad_night", "DlcName": "mpgunrunning", "ModificationsCount": 7}, {"Name": "mp_lad_day", "DlcName": "mpgunrunning", "ModificationsCount": 7}, {"Name": "mp_lad_judgment", "DlcName": "mpgunrunning", "ModificationsCount": 7}, {"Name": "mp_imx_intwaremed", "DlcName": "mpgunrunning", "ModificationsCount": 41}, {"Name": "mp_imx_intwaremed_office", "DlcName": "mpgunrunning", "ModificationsCount": 41}, {"Name": "mp_imx_mod_int_01", "DlcName": "mpgunrunning", "ModificationsCount": 30}, {"Name": "IMpExt_Interior_02", "DlcName": "mpgunrunning", "ModificationsCount": 57}, {"Name": "ImpExp_Interior_01", "DlcName": "mpgunrunning", "ModificationsCount": 55}, {"Name": "impexp_interior_01_lift", "DlcName": "mpgunrunning", "ModificationsCount": 56}, {"Name": "IMpExt_Interior_02_stair_cage", "DlcName": "mpgunrunning", "ModificationsCount": 57}, {"Name": "mp_gr_int01_white", "DlcName": "mpgunrunning", "ModificationsCount": 33}, {"Name": "mp_gr_int01_grey", "DlcName": "mpgunrunning", "ModificationsCount": 33}, {"Name": "mp_gr_int01_black", "DlcName": "mpgunrunning", "ModificationsCount": 33}, {"Name": "grdlc_int_02", "DlcName": "mpgunrunning", "ModificationsCount": 34}, {"Name": "mp_nightshark_shield_fp", "DlcName": "mpgunrunning", "ModificationsCount": 4}, {"Name": "grdlc_int_02_trailer_cave", "DlcName": "mpgunrunning", "ModificationsCount": 33}, {"Name": "li", "DlcName": "mpimportexport", "ModificationsCount": 32}, {"Name": "underwater", "DlcName": "mpimportexport", "ModificationsCount": 115}, {"Name": "underwater_deep", "DlcName": "mpimportexport", "ModificationsCount": 81}, {"Name": "NoAmbientmult", "DlcName": "mpimportexport", "ModificationsCount": 4}, {"Name": "superDARK", "DlcName": "mpimportexport", "ModificationsCount": 3}, {"Name": "CAMERA_BW", "DlcName": "mpimportexport", "ModificationsCount": 33}, {"Name": "Forest", "DlcName": "mpimportexport", "ModificationsCount": 30}, {"Name": "micheal", "DlcName": "mpimportexport", "ModificationsCount": 10}, {"Name": "TREVOR", "DlcName": "mpimportexport", "ModificationsCount": 15}, {"Name": "FRANKLIN", "DlcName": "mpimportexport", "ModificationsCount": 20}, {"Name": "Tunnel", "DlcName": "mpimportexport", "ModificationsCount": 13}, {"Name": "carpark", "DlcName": "mpimportexport", "ModificationsCount": 18}, {"Name": "NEW_abattoir", "DlcName": "mpimportexport", "ModificationsCount": 67}, {"Name": "Vagos", "DlcName": "mpimportexport", "ModificationsCount": 23}, {"Name": "cops", "DlcName": "mpimportexport", "ModificationsCount": 18}, {"Name": "Bikers", "DlcName": "mpimportexport", "ModificationsCount": 28}, {"Name": "BikersSPLASH", "DlcName": "mpimportexport", "ModificationsCount": 30}, {"Name": "VagosSPLASH", "DlcName": "mpimportexport", "ModificationsCount": 21}, {"Name": "CopsSPLASH", "DlcName": "mpimportexport", "ModificationsCount": 22}, {"Name": "VAGOS_new_garage", "DlcName": "mpimportexport", "ModificationsCount": 24}, {"Name": "VAGOS_new_hangout", "DlcName": "mpimportexport", "ModificationsCount": 26}, {"Name": "NEW_jewel", "DlcName": "mpimportexport", "ModificationsCount": 35}, {"Name": "frankilnsAUNTS_new", "DlcName": "mpimportexport", "ModificationsCount": 37}, {"Name": "frankilnsAUNTS_SUNdir", "DlcName": "mpimportexport", "ModificationsCount": 2}, {"Name": "StreetLighting", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "NEW_tunnels", "DlcName": "mpimportexport", "ModificationsCount": 26}, {"Name": "NEW_yellowtunnels", "DlcName": "mpimportexport", "ModificationsCount": 55}, {"Name": "NEW_tunnels_hole", "DlcName": "mpimportexport", "ModificationsCount": 31}, {"Name": "NEW_tunnels_ditch", "DlcName": "mpimportexport", "ModificationsCount": 30}, {"Name": "Paleto", "DlcName": "mpimportexport", "ModificationsCount": 6}, {"Name": "new_bank", "DlcName": "mpimportexport", "ModificationsCount": 17}, {"Name": "ReduceDrawDistance", "DlcName": "mpimportexport", "ModificationsCount": 10}, {"Name": "ReduceDrawDistanceMission", "DlcName": "mpimportexport", "ModificationsCount": 6}, {"Name": "lightpolution", "DlcName": "mpimportexport", "ModificationsCount": 16}, {"Name": "NEW_lesters", "DlcName": "mpimportexport", "ModificationsCount": 35}, {"Name": "ReduceDrawDistanceMAP", "DlcName": "mpimportexport", "ModificationsCount": 6}, {"Name": "reducewaterREF", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "garage", "DlcName": "mpimportexport", "ModificationsCount": 12}, {"Name": "LightPollutionHills", "DlcName": "mpimportexport", "ModificationsCount": 16}, {"Name": "NewMicheal", "DlcName": "mpimportexport", "ModificationsCount": 30}, {"Name": "NewMichealupstairs", "DlcName": "mpimportexport", "ModificationsCount": 20}, {"Name": "NewMichealstoilet", "DlcName": "mpimportexport", "ModificationsCount": 18}, {"Name": "NewMichealgirly", "DlcName": "mpimportexport", "ModificationsCount": 29}, {"Name": "WATER_port", "DlcName": "mpimportexport", "ModificationsCount": 60}, {"Name": "WATER_salton", "DlcName": "mpimportexport", "ModificationsCount": 68}, {"Name": "WATER_river", "DlcName": "mpimportexport", "ModificationsCount": 61}, {"Name": "FIB_interview", "DlcName": "mpimportexport", "ModificationsCount": 33}, {"Name": "NEW_station_unfinished", "DlcName": "mpimportexport", "ModificationsCount": 33}, {"Name": "cashdepot", "DlcName": "mpimportexport", "ModificationsCount": 30}, {"Name": "cashdepotEMERGENCY", "DlcName": "mpimportexport", "ModificationsCount": 36}, {"Name": "FrankilinsHOUSEhills", "DlcName": "mpimportexport", "ModificationsCount": 29}, {"Name": "HicksbarNEW", "DlcName": "mpimportexport", "ModificationsCount": 32}, {"Name": "NOdirectLight", "DlcName": "mpimportexport", "ModificationsCount": 0}, {"Name": "SALTONSEA", "DlcName": "mpimportexport", "ModificationsCount": 59}, {"Name": "TUNNEL_green", "DlcName": "mpimportexport", "ModificationsCount": 31}, {"Name": "NewMicheal_night", "DlcName": "mpimportexport", "ModificationsCount": 24}, {"Name": "WATER_muddy", "DlcName": "mpimportexport", "ModificationsCount": 69}, {"Name": "WATER_shore", "DlcName": "mpimportexport", "ModificationsCount": 22}, {"Name": "damage", "DlcName": "mpimportexport", "ModificationsCount": 11}, {"Name": "hitped", "DlcName": "mpimportexport", "ModificationsCount": 2}, {"Name": "dying", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "overwater", "DlcName": "mpimportexport", "ModificationsCount": 6}, {"Name": "whitenightlighting", "DlcName": "mpimportexport", "ModificationsCount": 6}, {"Name": "TUNNEL_yellow", "DlcName": "mpimportexport", "ModificationsCount": 32}, {"Name": "buildingTOP", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "WATER_lab", "DlcName": "mpimportexport", "ModificationsCount": 89}, {"Name": "cinema", "DlcName": "mpimportexport", "ModificationsCount": 36}, {"Name": "fireDEPT", "DlcName": "mpimportexport", "ModificationsCount": 34}, {"Name": "ranch", "DlcName": "mpimportexport", "ModificationsCount": 33}, {"Name": "TUNNEL_white", "DlcName": "mpimportexport", "ModificationsCount": 36}, {"Name": "V_recycle_mainroom", "DlcName": "mpimportexport", "ModificationsCount": 26}, {"Name": "V_recycle_dark", "DlcName": "mpimportexport", "ModificationsCount": 27}, {"Name": "V_recycle_light", "DlcName": "mpimportexport", "ModificationsCount": 26}, {"Name": "lightning_weak", "DlcName": "mpimportexport", "ModificationsCount": 33}, {"Name": "lightning_strong", "DlcName": "mpimportexport", "ModificationsCount": 36}, {"Name": "lightning_cloud", "DlcName": "mpimportexport", "ModificationsCount": 9}, {"Name": "gunclubrange", "DlcName": "mpimportexport", "ModificationsCount": 36}, {"Name": "NoAmbientmult_interior", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "FullAmbientmult_interior", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "StreetLightingJunction", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "StreetLightingtraffic", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "Multipayer_spectatorCam", "DlcName": "mpimportexport", "ModificationsCount": 18}, {"Name": "INT_NoAmbientmult", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "INT_NoAmbientmult_art", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "INT_FullAmbientmult", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "INT_FULLAmbientmult_art", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "INT_FULLAmbientmult_both", "DlcName": "mpimportexport", "ModificationsCount": 2}, {"Name": "INT_NoAmbientmult_both", "DlcName": "mpimportexport", "ModificationsCount": 2}, {"Name": "<PERSON><PERSON><PERSON>", "DlcName": "mpimportexport", "ModificationsCount": 20}, {"Name": "ReduceSSAO", "DlcName": "mpimportexport", "ModificationsCount": 0}, {"Name": "scope_zoom_in", "DlcName": "mpimportexport", "ModificationsCount": 5}, {"Name": "scope_zoom_out", "DlcName": "mpimportexport", "ModificationsCount": 5}, {"Name": "crane_cam", "DlcName": "mpimportexport", "ModificationsCount": 19}, {"Name": "WATER_silty", "DlcName": "mpimportexport", "ModificationsCount": 64}, {"Name": "Trevors_room", "DlcName": "mpimportexport", "ModificationsCount": 31}, {"Name": "Hint_cam", "DlcName": "mpimportexport", "ModificationsCount": 16}, {"Name": "venice_canal_tunnel", "DlcName": "mpimportexport", "ModificationsCount": 47}, {"Name": "blackNwhite", "DlcName": "mpimportexport", "ModificationsCount": 22}, {"Name": "projector", "DlcName": "mpimportexport", "ModificationsCount": 12}, {"Name": "paleto_opt", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "WAREHOUSE", "DlcName": "mpimportexport", "ModificationsCount": 27}, {"Name": "pulse", "DlcName": "mpimportexport", "ModificationsCount": 20}, {"Name": "sleeping", "DlcName": "mpimportexport", "ModificationsCount": 10}, {"Name": "INT_garage", "DlcName": "mpimportexport", "ModificationsCount": 19}, {"Name": "nextgen", "DlcName": "mpimportexport", "ModificationsCount": 5}, {"Name": "crane_cam_cinematic", "DlcName": "mpimportexport", "ModificationsCount": 7}, {"Name": "TUNNEL_orange", "DlcName": "mpimportexport", "ModificationsCount": 33}, {"Name": "traffic_skycam", "DlcName": "mpimportexport", "ModificationsCount": 30}, {"Name": "powerstation", "DlcName": "mpimportexport", "ModificationsCount": 7}, {"Name": "SAWMILL", "DlcName": "mpimportexport", "ModificationsCount": 9}, {"Name": "LODmult_global_reduce", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "LODmult_HD_orphan_reduce", "DlcName": "mpimportexport", "ModificationsCount": 2}, {"Name": "LODmult_HD_orphan_LOD_reduce", "DlcName": "mpimportexport", "ModificationsCount": 3}, {"Name": "LODmult_LOD_reduce", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "LODmult_SLOD1_reduce", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "LODmult_SLOD2_reduce", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "LODmult_SLOD3_reduce", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "NewMicheal_upstairs", "DlcName": "mpimportexport", "ModificationsCount": 24}, {"Name": "micheals_lightsOFF", "DlcName": "mpimportexport", "ModificationsCount": 2}, {"Name": "telescope", "DlcName": "mpimportexport", "ModificationsCount": 25}, {"Name": "WATER_silverlake", "DlcName": "mpimportexport", "ModificationsCount": 56}, {"Name": "WATER _lab_cooling", "DlcName": "mpimportexport", "ModificationsCount": 95}, {"Name": "baseTONEMAPPING", "DlcName": "mpimportexport", "ModificationsCount": 16}, {"Name": "WATER_salton_bottom", "DlcName": "mpimportexport", "ModificationsCount": 37}, {"Name": "new_stripper_changing", "DlcName": "mpimportexport", "ModificationsCount": 29}, {"Name": "underwater_deep_clear", "DlcName": "mpimportexport", "ModificationsCount": 4}, {"Name": "prologue_ending_fog", "DlcName": "mpimportexport", "ModificationsCount": 25}, {"Name": "graveyard_shootout", "DlcName": "mpimportexport", "ModificationsCount": 16}, {"Name": "morebloom", "DlcName": "mpimportexport", "ModificationsCount": 3}, {"Name": "LIGHTSreduceFALLOFF", "DlcName": "mpimportexport", "ModificationsCount": 0}, {"Name": "INT_posh_hairdresser", "DlcName": "mpimportexport", "ModificationsCount": 23}, {"Name": "V_strip_office", "DlcName": "mpimportexport", "ModificationsCount": 28}, {"Name": "sunglasses", "DlcName": "mpimportexport", "ModificationsCount": 15}, {"Name": "vespucci_garage", "DlcName": "mpimportexport", "ModificationsCount": 34}, {"Name": "half_direct", "DlcName": "mpimportexport", "ModificationsCount": 3}, {"Name": "carpark_dt1_03", "DlcName": "mpimportexport", "ModificationsCount": 20}, {"Name": "tunnel_id1_11", "DlcName": "mpimportexport", "ModificationsCount": 20}, {"Name": "reducelightingcost", "DlcName": "mpimportexport", "ModificationsCount": 2}, {"Name": "NOrain", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "morgue_dark", "DlcName": "mpimportexport", "ModificationsCount": 44}, {"Name": "CS3_rail_tunnel", "DlcName": "mpimportexport", "ModificationsCount": 19}, {"Name": "new_tunnels_entrance", "DlcName": "mpimportexport", "ModificationsCount": 47}, {"Name": "spectator1", "DlcName": "mpimportexport", "ModificationsCount": 41}, {"Name": "spectator2", "DlcName": "mpimportexport", "ModificationsCount": 34}, {"Name": "spectator3", "DlcName": "mpimportexport", "ModificationsCount": 33}, {"Name": "spectator4", "DlcName": "mpimportexport", "ModificationsCount": 36}, {"Name": "spectator5", "DlcName": "mpimportexport", "ModificationsCount": 63}, {"Name": "spectator6", "DlcName": "mpimportexport", "ModificationsCount": 43}, {"Name": "spectator7", "DlcName": "mpimportexport", "ModificationsCount": 48}, {"Name": "spectator8", "DlcName": "mpimportexport", "ModificationsCount": 37}, {"Name": "spectator9", "DlcName": "mpimportexport", "ModificationsCount": 53}, {"Name": "spectator10", "DlcName": "mpimportexport", "ModificationsCount": 31}, {"Name": "INT_NOdirectLight", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "WATER_resevoir", "DlcName": "mpimportexport", "ModificationsCount": 68}, {"Name": "WATER_hills", "DlcName": "mpimportexport", "ModificationsCount": 66}, {"Name": "WATER_militaryPOOP", "DlcName": "mpimportexport", "ModificationsCount": 62}, {"Name": "NEW_ornate_bank", "DlcName": "mpimportexport", "ModificationsCount": 43}, {"Name": "NEW_ornate_bank_safe", "DlcName": "mpimportexport", "ModificationsCount": 46}, {"Name": "NEW_ornate_bank_entrance", "DlcName": "mpimportexport", "ModificationsCount": 38}, {"Name": "NEW_ornate_bank_office", "DlcName": "mpimportexport", "ModificationsCount": 46}, {"Name": "LODmult_global_reduce_NOHD", "DlcName": "mpimportexport", "ModificationsCount": 6}, {"Name": "interior_WATER_lighting", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "gorge_reflectionoffset", "DlcName": "mpimportexport", "ModificationsCount": 2}, {"Name": "eyeINtheSKY", "DlcName": "mpimportexport", "ModificationsCount": 17}, {"Name": "resvoire_reflection", "DlcName": "mpimportexport", "ModificationsCount": 6}, {"Name": "NO_weather", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "prologue_ext_art_amb", "DlcName": "mpimportexport", "ModificationsCount": 12}, {"Name": "prologue_shootout", "DlcName": "mpimportexport", "ModificationsCount": 20}, {"Name": "heathaze", "DlcName": "mpimportexport", "ModificationsCount": 22}, {"Name": "KT_underpass", "DlcName": "mpimportexport", "ModificationsCount": 25}, {"Name": "INT_nowaterREF", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "carMOD_underpass", "DlcName": "mpimportexport", "ModificationsCount": 28}, {"Name": "refit", "DlcName": "mpimportexport", "ModificationsCount": 34}, {"Name": "NO_streetAmbient", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "NO_coronas", "DlcName": "mpimportexport", "ModificationsCount": 2}, {"Name": "epsilion", "DlcName": "mpimportexport", "ModificationsCount": 24}, {"Name": "WATER_refmap_high", "DlcName": "mpimportexport", "ModificationsCount": 0}, {"Name": "WATER_refmap_med", "DlcName": "mpimportexport", "ModificationsCount": 0}, {"Name": "WATER_refmap_low", "DlcName": "mpimportexport", "ModificationsCount": 0}, {"Name": "WATER_refmap_verylow", "DlcName": "mpimportexport", "ModificationsCount": 0}, {"Name": "WATER_refmap_poolside", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "WATER_refmap_silverlake", "DlcName": "mpimportexport", "ModificationsCount": 0}, {"Name": "WATER_refmap_venice", "DlcName": "mpimportexport", "ModificationsCount": 0}, {"Name": "FORdoron_delete", "DlcName": "mpimportexport", "ModificationsCount": 4}, {"Name": "NO_fog_alpha", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "V_strip_nofog", "DlcName": "mpimportexport", "ModificationsCount": 28}, {"Name": "METRO_Tunnels", "DlcName": "mpimportexport", "ModificationsCount": 59}, {"Name": "METRO_Tunnels_entrance", "DlcName": "mpimportexport", "ModificationsCount": 31}, {"Name": "METRO_platform", "DlcName": "mpimportexport", "ModificationsCount": 30}, {"Name": "STRIP_stage", "DlcName": "mpimportexport", "ModificationsCount": 55}, {"Name": "STRIP_office", "DlcName": "mpimportexport", "ModificationsCount": 30}, {"Name": "STRIP_changing", "DlcName": "mpimportexport", "ModificationsCount": 30}, {"Name": "INT_NO_fogALPHA", "DlcName": "mpimportexport", "ModificationsCount": 3}, {"Name": "STRIP_nofog", "DlcName": "mpimportexport", "ModificationsCount": 30}, {"Name": "INT_streetlighting", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "ch2_tunnel_whitelight", "DlcName": "mpimportexport", "ModificationsCount": 6}, {"Name": "AmbientPUSH", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "ship_lighting", "DlcName": "mpimportexport", "ModificationsCount": 4}, {"Name": "powerplant_nightlight", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "paleto_nightlight", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "militarybase_nightlight", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "sandyshore_nightlight", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "jewel_gas", "DlcName": "mpimportexport", "ModificationsCount": 0}, {"Name": "WATER_refmap_off", "DlcName": "mpimportexport", "ModificationsCount": 13}, {"Name": "trailer_explosion_optimise", "DlcName": "mpimportexport", "ModificationsCount": 19}, {"Name": "nervousRON_fog", "DlcName": "mpimportexport", "ModificationsCount": 7}, {"Name": "DONT_overide_sunpos", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "gallery_refmod", "DlcName": "mpimportexport", "ModificationsCount": 20}, {"Name": "prison_nightlight", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "multiplayer_ped_fight", "DlcName": "mpimportexport", "ModificationsCount": 4}, {"Name": "ship_explosion_underwater", "DlcName": "mpimportexport", "ModificationsCount": 20}, {"Name": "EXTRA_bouncelight", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "secret_camera", "DlcName": "mpimportexport", "ModificationsCount": 33}, {"Name": "canyon_mission", "DlcName": "mpimportexport", "ModificationsCount": 23}, {"Name": "gorge_reflection_gpu", "DlcName": "mpimportexport", "ModificationsCount": 4}, {"Name": "subBASE_water_ref", "DlcName": "mpimportexport", "ModificationsCount": 13}, {"Name": "poolsidewaterreflection2", "DlcName": "mpimportexport", "ModificationsCount": 13}, {"Name": "CUSTOM_streetlight", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "ufo", "DlcName": "mpimportexport", "ModificationsCount": 32}, {"Name": "ship_explosion_underwater", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "lab_none_exit", "DlcName": "mpimportexport", "ModificationsCount": 24}, {"Name": "FinaleBankexit", "DlcName": "mpimportexport", "ModificationsCount": 28}, {"Name": "prologue_reflection_opt", "DlcName": "mpimportexport", "ModificationsCount": 4}, {"Name": "tunnel_entrance", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "tunnel_entrance_INT", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "id1_11_tunnel", "DlcName": "mpimportexport", "ModificationsCount": 31}, {"Name": "reflection_correct_ambient", "DlcName": "mpimportexport", "ModificationsCount": 3}, {"Name": "scanline_cam_cheap", "DlcName": "mpimportexport", "ModificationsCount": 12}, {"Name": "scanline_cam", "DlcName": "mpimportexport", "ModificationsCount": 16}, {"Name": "VC_tunnel_entrance", "DlcName": "mpimportexport", "ModificationsCount": 0}, {"Name": "WATER_REF_malibu", "DlcName": "mpimportexport", "ModificationsCount": 13}, {"Name": "carpark_dt1_02", "DlcName": "mpimportexport", "ModificationsCount": 31}, {"Name": "FIB_interview_optimise", "DlcName": "mpimportexport", "ModificationsCount": 21}, {"Name": "Prologue_shootout_opt", "DlcName": "mpimportexport", "ModificationsCount": 10}, {"Name": "hangar_lightsmod", "DlcName": "mpimportexport", "ModificationsCount": 2}, {"Name": "plane_inside_mode", "DlcName": "mpimportexport", "ModificationsCount": 34}, {"Name": "eatra_bouncelight_beach", "DlcName": "mpimportexport", "ModificationsCount": 5}, {"Name": "downtown_FIB_cascades_opt", "DlcName": "mpimportexport", "ModificationsCount": 3}, {"Name": "jewel_optim", "DlcName": "mpimportexport", "ModificationsCount": 2}, {"Name": "gorge_reflectionoffset2", "DlcName": "mpimportexport", "ModificationsCount": 2}, {"Name": "ufo_deathray", "DlcName": "mpimportexport", "ModificationsCount": 59}, {"Name": "PORT_heist_underwater", "DlcName": "mpimportexport", "ModificationsCount": 5}, {"Name": "TUNNEL_orange_exterior", "DlcName": "mpimportexport", "ModificationsCount": 17}, {"Name": "hillstunnel", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "jewelry_entrance_INT", "DlcName": "mpimportexport", "ModificationsCount": 2}, {"Name": "jewelry_entrance", "DlcName": "mpimportexport", "ModificationsCount": 2}, {"Name": "jewelry_entrance_INT_fog", "DlcName": "mpimportexport", "ModificationsCount": 2}, {"Name": "TUNNEL_yellow_ext", "DlcName": "mpimportexport", "ModificationsCount": 12}, {"Name": "NEW_jewel_EXIT", "DlcName": "mpimportexport", "ModificationsCount": 35}, {"Name": "services_nightlight", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "CS1_railwayB_tunnel", "DlcName": "mpimportexport", "ModificationsCount": 21}, {"Name": "TUNNEL_green_ext", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "CAMERA_secuirity", "DlcName": "mpimportexport", "ModificationsCount": 33}, {"Name": "CAMERA_secuirity_FUZZ", "DlcName": "mpimportexport", "ModificationsCount": 33}, {"Name": "int_hospital_small", "DlcName": "mpimportexport", "ModificationsCount": 26}, {"Name": "int_hospital_dark", "DlcName": "mpimportexport", "ModificationsCount": 26}, {"Name": "plaza_carpark", "DlcName": "mpimportexport", "ModificationsCount": 27}, {"Name": "gen_bank", "DlcName": "mpimportexport", "ModificationsCount": 27}, {"Name": "nightvision", "DlcName": "mpimportexport", "ModificationsCount": 30}, {"Name": "WATER_cove", "DlcName": "mpimportexport", "ModificationsCount": 116}, {"Name": "glasses_Darkblue", "DlcName": "mpimportexport", "ModificationsCount": 15}, {"Name": "glasses_VISOR", "DlcName": "mpimportexport", "ModificationsCount": 18}, {"Name": "heist_boat", "DlcName": "mpimportexport", "ModificationsCount": 15}, {"Name": "heist_boat_norain", "DlcName": "mpimportexport", "ModificationsCount": 16}, {"Name": "heist_boat_engineRoom", "DlcName": "mpimportexport", "ModificationsCount": 15}, {"Name": "buggy_shack", "DlcName": "mpimportexport", "ModificationsCount": 28}, {"Name": "mineshaft", "DlcName": "mpimportexport", "ModificationsCount": 29}, {"Name": "NG_first", "DlcName": "mpimportexport", "ModificationsCount": 11}, {"Name": "glasses_Scuba", "DlcName": "mpimportexport", "ModificationsCount": 9}, {"Name": "mugShot", "DlcName": "mpimportexport", "ModificationsCount": 53}, {"Name": "Glasses_BlackOut", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "winning_room", "DlcName": "mpimportexport", "ModificationsCount": 136}, {"Name": "mugShot_lineup", "DlcName": "mpimportexport", "ModificationsCount": 54}, {"Name": "MPApartHigh_palnning", "DlcName": "mpimportexport", "ModificationsCount": 56}, {"Name": "v_dark", "DlcName": "mpimportexport", "ModificationsCount": 67}, {"Name": "vehicle_subint", "DlcName": "mpimportexport", "ModificationsCount": 12}, {"Name": "Carpark_MP_exit", "DlcName": "mpimportexport", "ModificationsCount": 13}, {"Name": "EXT_FULLAmbientmult_art", "DlcName": "mpimportexport", "ModificationsCount": 1}, {"Name": "new_MP_Garage_L", "DlcName": "mpimportexport", "ModificationsCount": 45}, {"Name": "fp_vig_black", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "fp_vig_brown", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "fp_vig_gray", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "fp_vig_blue", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "fp_vig_red", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "fp_vig_green", "DlcName": "mpimportexport", "ModificationsCount": 8}, {"Name": "INT_trailer_cinema", "DlcName": "mpimportexport", "ModificationsCount": 30}, {"Name": "heliGunCam", "DlcName": "mpimportexport", "ModificationsCount": 20}, {"Name": "INT_smshop", "DlcName": "mpimportexport", "ModificationsCount": 38}, {"Name": "INT_mall", "DlcName": "mpimportexport", "ModificationsCount": 48}, {"Name": "Mp_<PERSON>ilts", "DlcName": "mpimportexport", "ModificationsCount": 29}, {"Name": "Mp_Stilts_gym", "DlcName": "mpimportexport", "ModificationsCount": 30}, {"Name": "Mp_Stilts2", "DlcName": "mpimportexport", "ModificationsCount": 29}, {"Name": "Mp_Stilts_gym2", "DlcName": "mpimportexport", "ModificationsCount": 30}, {"Name": "MPApart_H_01", "DlcName": "mpimportexport", "ModificationsCount": 53}, {"Name": "MPApart_H_01_gym", "DlcName": "mpimportexport", "ModificationsCount": 53}, {"Name": "MP_H_01_Study", "DlcName": "mpimportexport", "ModificationsCount": 53}, {"Name": "MP_H_01_Bedroom", "DlcName": "mpimportexport", "ModificationsCount": 53}, {"Name": "MP_H_01_Bathroom", "DlcName": "mpimportexport", "ModificationsCount": 53}, {"Name": "MP_H_01_New", "DlcName": "mpimportexport", "ModificationsCount": 52}, {"Name": "MP_H_01_New_Bedroom", "DlcName": "mpimportexport", "ModificationsCount": 53}, {"Name": "MP_H_01_New_Bathroom", "DlcName": "mpimportexport", "ModificationsCount": 53}, {"Name": "MP_H_01_New_Study", "DlcName": "mpimportexport", "ModificationsCount": 53}, {"Name": "INT_smshop_inMOD", "DlcName": "mpimportexport", "ModificationsCount": 24}, {"Name": "NoPedLight", "DlcName": "mpimportexport", "ModificationsCount": 4}, {"Name": "morgue_dark_ovr", "DlcName": "mpimportexport", "ModificationsCount": 45}, {"Name": "INT_smshop_outdoor_bloom", "DlcName": "mpimportexport", "ModificationsCount": 3}, {"Name": "INT_smshop_indoor_bloom", "DlcName": "mpimportexport", "ModificationsCount": 3}, {"Name": "MP_H_02", "DlcName": "mpimportexport", "ModificationsCount": 53}, {"Name": "MP_H_04", "DlcName": "mpimportexport", "ModificationsCount": 53}, {"Name": "Mp_Stilts2_bath", "DlcName": "mpimportexport", "ModificationsCount": 30}, {"Name": "mp_h_05", "DlcName": "mpimportexport", "ModificationsCount": 53}, {"Name": "mp_h_07", "DlcName": "mpimportexport", "ModificationsCount": 53}, {"Name": "MP_H_06", "DlcName": "mpimportexport", "ModificationsCount": 53}, {"Name": "mp_h_08", "DlcName": "mpimportexport", "ModificationsCount": 53}, {"Name": "yacht_DLC", "DlcName": "mpimportexport", "ModificationsCount": 20}, {"Name": "mp_exec_office_01", "DlcName": "mpimportexport", "ModificationsCount": 26}, {"Name": "mp_exec_warehouse_01", "DlcName": "mpimportexport", "ModificationsCount": 32}, {"Name": "mp_exec_office_02", "DlcName": "mpimportexport", "ModificationsCount": 27}, {"Name": "mp_exec_office_03", "DlcName": "mpimportexport", "ModificationsCount": 29}, {"Name": "mp_exec_office_04", "DlcName": "mpimportexport", "ModificationsCount": 27}, {"Name": "mp_exec_office_05", "DlcName": "mpimportexport", "ModificationsCount": 28}, {"Name": "mp_exec_office_06", "DlcName": "mpimportexport", "ModificationsCount": 27}, {"Name": "mp_exec_office_03_blue", "DlcName": "mpimportexport", "ModificationsCount": 32}, {"Name": "mp_exec_office_03C", "DlcName": "mpimportexport", "ModificationsCount": 32}, {"Name": "mp_bkr_int01_garage", "DlcName": "mpimportexport", "ModificationsCount": 33}, {"Name": "mp_bkr_int01_transition", "DlcName": "mpimportexport", "ModificationsCount": 33}, {"Name": "mp_bkr_int01_small_rooms", "DlcName": "mpimportexport", "ModificationsCount": 35}, {"Name": "mp_bkr_int02_garage", "DlcName": "mpimportexport", "ModificationsCount": 23}, {"Name": "mp_bkr_int02_hangout", "DlcName": "mpimportexport", "ModificationsCount": 24}, {"Name": "mp_bkr_int02_small_rooms", "DlcName": "mpimportexport", "ModificationsCount": 28}, {"Name": "mp_bkr_ware01", "DlcName": "mpimportexport", "ModificationsCount": 32}, {"Name": "mp_bkr_ware02_standard", "DlcName": "mpimportexport", "ModificationsCount": 29}, {"Name": "mp_bkr_ware02_upgrade", "DlcName": "mpimportexport", "ModificationsCount": 29}, {"Name": "mp_bkr_ware02_dry", "DlcName": "mpimportexport", "ModificationsCount": 22}, {"Name": "mp_bkr_ware03_basic", "DlcName": "mpimportexport", "ModificationsCount": 29}, {"Name": "mp_bkr_ware03_upgrade", "DlcName": "mpimportexport", "ModificationsCount": 28}, {"Name": "mp_bkr_ware04", "DlcName": "mpimportexport", "ModificationsCount": 25}, {"Name": "mp_bkr_ware05", "DlcName": "mpimportexport", "ModificationsCount": 30}, {"Name": "mp_lad_night", "DlcName": "mpimportexport", "ModificationsCount": 7}, {"Name": "mp_lad_day", "DlcName": "mpimportexport", "ModificationsCount": 7}, {"Name": "mp_lad_judgment", "DlcName": "mpimportexport", "ModificationsCount": 7}, {"Name": "mp_imx_intwaremed", "DlcName": "mpimportexport", "ModificationsCount": 41}, {"Name": "mp_imx_intwaremed_office", "DlcName": "mpimportexport", "ModificationsCount": 41}, {"Name": "mp_imx_mod_int_01", "DlcName": "mpimportexport", "ModificationsCount": 30}, {"Name": "IMpExt_Interior_02", "DlcName": "mpimportexport", "ModificationsCount": 57}, {"Name": "ImpExp_Interior_01", "DlcName": "mpimportexport", "ModificationsCount": 55}, {"Name": "impexp_interior_01_lift", "DlcName": "mpimportexport", "ModificationsCount": 56}, {"Name": "IMpExt_Interior_02_stair_cage", "DlcName": "mpimportexport", "ModificationsCount": 57}]