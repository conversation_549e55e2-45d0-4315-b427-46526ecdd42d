# Dolu Tool
This is a FiveM resource providing a bunch of features to help you developing your scripts and mods.
Any contribution will be appreciated!

## Installation
Please read the [Documentation](https://dolutattoo.github.io/docs/category/dolu_tool) and follow the install steps.

## Features
- Locale system from ox_lib
- Checks and displays updates in the menu header ([see this picture](https://user-images.githubusercontent.com/47056777/213062611-3397e226-8996-40a3-8da7-9fc072a84aa3.jpg))
- Target objects to copy coords, delete or move them using the gizmo (requires [ox_target](https://github.com/overextended/ox_target))
- Permission system (check config.lua)
### Home
  - Display coords
  - Copy coords
  - Set coords
  - Save coords as location
  - Go to the last location
  - Display basic interior informations
  - Quick actions buttons
### World
  - Set time
  - Freeze time
  - Set weather
  - Freeze weather
### Interior (MLO)
  - Display interior informations
  - Portals debugging
  - Check portal flags with descriptions
  - Set portal flags by checking boxes
  - Room from/to infos
### Object spawner
  - Spawn objects and move them using a nice Gizmo
  - Keep all spawned objects in a list
  - Various options available for spawned entities
### Locations
  - Browse all vanilla interiors
  - Copy coords/teleport
  - Create custom locations, saved in server cache (kvp)
  - Delete/Rename custom locations
  - Filter search to show/hide custom/vanilla locations
### Peds
  - Browse all vanilla peds
  - Preview images
  - Set peds
  - Copy name/hash
### Vehicles
  - Browse all vanilla vehicles
  - Preview images
  - Spawn vehicle
  - Copy name/hash
### Weapons
  - Browse all vanilla weapons
  - Preview images
  - Give weapons (with ox_inventory support)
  - Copy name/hash
### Audio
  - Draw near vanilla Static Emitters
  - Set draw distance
  - Get info from closest vanilla Static Emitter
  - Set radio station, play or stop closest vanilla Static Emitter

# Preview
![1045_18-01-23_02-27](https://user-images.githubusercontent.com/47056777/213062596-cb40e714-f941-446b-8387-beaa9cf4ec37.jpg)
![1046_18-01-23_02-27](https://user-images.githubusercontent.com/47056777/213062599-febc9b70-01c8-42ca-9bc8-e0b91c4b8ee5.jpg)
![1055_18-01-23_02-49](https://user-images.githubusercontent.com/47056777/213062611-3397e226-8996-40a3-8da7-9fc072a84aa3.jpg)
![1047_18-01-23_02-29](https://user-images.githubusercontent.com/47056777/213062612-81271c1c-84de-4b26-bfb2-e44b73145127.jpg)
![1049_18-01-23_02-30](https://user-images.githubusercontent.com/47056777/213062616-776be0a9-415b-4369-b38a-68fa398c4a19.jpg)
![1050_18-01-23_02-31](https://user-images.githubusercontent.com/47056777/213062620-8828ae32-e418-436d-9387-557d9c72e5a1.jpg)
![1051_18-01-23_02-32](https://user-images.githubusercontent.com/47056777/213062622-30768378-10eb-43fa-ab52-22379c33332c.jpg)
![1052_18-01-23_02-32](https://user-images.githubusercontent.com/47056777/213062625-c8f9d8e1-0e18-4ebb-9069-f2fee67c650f.jpg)
![1053_18-01-23_02-33](https://user-images.githubusercontent.com/47056777/213062626-eed79966-584c-4052-9849-671bb7ac7765.jpg)
![1054_18-01-23_02-33](https://user-images.githubusercontent.com/47056777/213062627-d9d70a02-942e-47e1-abac-2442a3e1dc8f.jpg)
![1054_18-01-23_02-33](https://i.imgur.com/dg4zAbi.jpg)

# Credits
This tool could not exists without the help of
- **[AnthonyTCS](https://github.com/AnthonyTCS)**
- **[Tiwabs](https://github.com/Tiwabs)**
- **[Lentokone](https://github.com/Aik-10)**
- **[Luke](https://github.com/Lukewastakenn)**
- **[Seazer](https://github.com/Biromain)**
