{"name": "@types/node-fetch", "version": "2.5.12", "description": "TypeScript definitions for node-fetch", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-fetch", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/torstenwerner", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nikcorg", "githubUsername": "nikcorg"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/vinaybedre", "githubUsername": "v<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kyranet", "githubUsername": "kyranet"}, {"name": "<PERSON>", "url": "https://github.com/Andrew<PERSON><PERSON>ham", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonLi914", "githubUsername": "JasonLi914"}, {"name": "<PERSON>", "url": "https://github.com/southpolesteve", "githubUsername": "southpolesteve"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss", "githubUsername": "ExE-Boss"}, {"name": "<PERSON>", "url": "https://github.com/alexandrusavin", "githubUsername": "alexa<PERSON><PERSON><PERSON>n"}, {"name": "<PERSON>", "url": "https://github.com/OmgImAlexis", "githubUsername": "OmgImAlexis"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/kbkk", "githubUsername": "kbkk"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node-fetch"}, "scripts": {}, "dependencies": {"@types/node": "*", "form-data": "^3.0.0"}, "typesPublisherContentHash": "cb95d0ddf740cecae06afa5d31c7c25945a352c24620ceb89c3b0da970a3d5e0", "typeScriptVersion": "3.6"}