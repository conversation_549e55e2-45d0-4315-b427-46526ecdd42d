{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["/**\n * @internal\n */\nexport interface CollectionConstructor {\n\tnew (): Collection<unknown, unknown>;\n\tnew <K, V>(entries?: ReadonlyArray<readonly [K, V]> | null): Collection<K, V>;\n\tnew <K, V>(iterable: Iterable<readonly [K, V]>): Collection<K, V>;\n\treadonly prototype: Collection<unknown, unknown>;\n\treadonly [Symbol.species]: CollectionConstructor;\n}\n\n/**\n * Separate interface for the constructor so that emitted js does not have a constructor that overwrites itself\n *\n * @internal\n */\nexport interface Collection<K, V> extends Map<K, V> {\n\tconstructor: CollectionConstructor;\n}\n\n/**\n * A Map with additional utility methods. This is used throughout discord.js rather than Arrays for anything that has\n * an ID, for significantly improved performance and ease-of-use.\n */\nexport class Collection<K, V> extends Map<K, V> {\n\tpublic static readonly default: typeof Collection = Collection;\n\n\t/**\n\t * Obtains the value of the given key if it exists, otherwise sets and returns the value provided by the default value generator.\n\t *\n\t * @param key The key to get if it exists, or set otherwise\n\t * @param defaultValueGenerator A function that generates the default value\n\t *\n\t * @example\n\t * collection.ensure(guildId, () => defaultGuildConfig);\n\t */\n\tpublic ensure(key: K, defaultValueGenerator: (key: K, collection: this) => V): V {\n\t\tif (this.has(key)) return this.get(key)!;\n\t\tconst defaultValue = defaultValueGenerator(key, this);\n\t\tthis.set(key, defaultValue);\n\t\treturn defaultValue;\n\t}\n\n\t/**\n\t * Checks if all of the elements exist in the collection.\n\t *\n\t * @param keys - The keys of the elements to check for\n\t *\n\t * @returns `true` if all of the elements exist, `false` if at least one does not exist.\n\t */\n\tpublic hasAll(...keys: K[]) {\n\t\treturn keys.every((k) => super.has(k));\n\t}\n\n\t/**\n\t * Checks if any of the elements exist in the collection.\n\t *\n\t * @param keys - The keys of the elements to check for\n\t *\n\t * @returns `true` if any of the elements exist, `false` if none exist.\n\t */\n\tpublic hasAny(...keys: K[]) {\n\t\treturn keys.some((k) => super.has(k));\n\t}\n\n\t/**\n\t * Obtains the first value(s) in this collection.\n\t *\n\t * @param amount Amount of values to obtain from the beginning\n\t *\n\t * @returns A single value if no amount is provided or an array of values, starting from the end if amount is negative\n\t */\n\tpublic first(): V | undefined;\n\tpublic first(amount: number): V[];\n\tpublic first(amount?: number): V | V[] | undefined {\n\t\tif (typeof amount === 'undefined') return this.values().next().value;\n\t\tif (amount < 0) return this.last(amount * -1);\n\t\tamount = Math.min(this.size, amount);\n\t\tconst iter = this.values();\n\t\treturn Array.from({ length: amount }, (): V => iter.next().value);\n\t}\n\n\t/**\n\t * Obtains the first key(s) in this collection.\n\t *\n\t * @param amount Amount of keys to obtain from the beginning\n\t *\n\t * @returns A single key if no amount is provided or an array of keys, starting from the end if\n\t * amount is negative\n\t */\n\tpublic firstKey(): K | undefined;\n\tpublic firstKey(amount: number): K[];\n\tpublic firstKey(amount?: number): K | K[] | undefined {\n\t\tif (typeof amount === 'undefined') return this.keys().next().value;\n\t\tif (amount < 0) return this.lastKey(amount * -1);\n\t\tamount = Math.min(this.size, amount);\n\t\tconst iter = this.keys();\n\t\treturn Array.from({ length: amount }, (): K => iter.next().value);\n\t}\n\n\t/**\n\t * Obtains the last value(s) in this collection.\n\t *\n\t * @param amount Amount of values to obtain from the end\n\t *\n\t * @returns A single value if no amount is provided or an array of values, starting from the start if\n\t * amount is negative\n\t */\n\tpublic last(): V | undefined;\n\tpublic last(amount: number): V[];\n\tpublic last(amount?: number): V | V[] | undefined {\n\t\tconst arr = [...this.values()];\n\t\tif (typeof amount === 'undefined') return arr[arr.length - 1];\n\t\tif (amount < 0) return this.first(amount * -1);\n\t\tif (!amount) return [];\n\t\treturn arr.slice(-amount);\n\t}\n\n\t/**\n\t * Obtains the last key(s) in this collection.\n\t *\n\t * @param amount Amount of keys to obtain from the end\n\t *\n\t * @returns A single key if no amount is provided or an array of keys, starting from the start if\n\t * amount is negative\n\t */\n\tpublic lastKey(): K | undefined;\n\tpublic lastKey(amount: number): K[];\n\tpublic lastKey(amount?: number): K | K[] | undefined {\n\t\tconst arr = [...this.keys()];\n\t\tif (typeof amount === 'undefined') return arr[arr.length - 1];\n\t\tif (amount < 0) return this.firstKey(amount * -1);\n\t\tif (!amount) return [];\n\t\treturn arr.slice(-amount);\n\t}\n\n\t/**\n\t * Identical to [Array.at()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/at).\n\t * Returns the item at a given index, allowing for positive and negative integers.\n\t * Negative integers count back from the last item in the collection.\n\t *\n\t * @param index The index of the element to obtain\n\t */\n\tpublic at(index: number) {\n\t\tindex = Math.floor(index);\n\t\tconst arr = [...this.values()];\n\t\treturn arr.at(index);\n\t}\n\n\t/**\n\t * Identical to [Array.at()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/at).\n\t * Returns the key at a given index, allowing for positive and negative integers.\n\t * Negative integers count back from the last item in the collection.\n\t *\n\t * @param index The index of the key to obtain\n\t */\n\tpublic keyAt(index: number) {\n\t\tindex = Math.floor(index);\n\t\tconst arr = [...this.keys()];\n\t\treturn arr.at(index);\n\t}\n\n\t/**\n\t * Obtains unique random value(s) from this collection.\n\t *\n\t * @param amount Amount of values to obtain randomly\n\t *\n\t * @returns A single value if no amount is provided or an array of values\n\t */\n\tpublic random(): V | undefined;\n\tpublic random(amount: number): V[];\n\tpublic random(amount?: number): V | V[] | undefined {\n\t\tconst arr = [...this.values()];\n\t\tif (typeof amount === 'undefined') return arr[Math.floor(Math.random() * arr.length)];\n\t\tif (!arr.length || !amount) return [];\n\t\treturn Array.from(\n\t\t\t{ length: Math.min(amount, arr.length) },\n\t\t\t(): V => arr.splice(Math.floor(Math.random() * arr.length), 1)[0],\n\t\t);\n\t}\n\n\t/**\n\t * Obtains unique random key(s) from this collection.\n\t *\n\t * @param amount Amount of keys to obtain randomly\n\t *\n\t * @returns A single key if no amount is provided or an array\n\t */\n\tpublic randomKey(): K | undefined;\n\tpublic randomKey(amount: number): K[];\n\tpublic randomKey(amount?: number): K | K[] | undefined {\n\t\tconst arr = [...this.keys()];\n\t\tif (typeof amount === 'undefined') return arr[Math.floor(Math.random() * arr.length)];\n\t\tif (!arr.length || !amount) return [];\n\t\treturn Array.from(\n\t\t\t{ length: Math.min(amount, arr.length) },\n\t\t\t(): K => arr.splice(Math.floor(Math.random() * arr.length), 1)[0],\n\t\t);\n\t}\n\n\t/**\n\t * Identical to [Array.reverse()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/reverse)\n\t * but returns a Collection instead of an Array.\n\t */\n\tpublic reverse() {\n\t\tconst entries = [...this.entries()].reverse();\n\t\tthis.clear();\n\t\tfor (const [key, value] of entries) this.set(key, value);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Searches for a single item where the given function returns a truthy value. This behaves like\n\t * [Array.find()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/find).\n\t * <warn>All collections used in Discord.js are mapped using their `id` property, and if you want to find by id you\n\t * should use the `get` method. See\n\t * [MDN](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map/get) for details.</warn>\n\t *\n\t * @param fn The function to test with (should return boolean)\n\t * @param thisArg Value to use as `this` when executing function\n\t *\n\t * @example\n\t * collection.find(user => user.username === 'Bob');\n\t */\n\tpublic find<V2 extends V>(fn: (value: V, key: K, collection: this) => value is V2): V2 | undefined;\n\tpublic find(fn: (value: V, key: K, collection: this) => boolean): V | undefined;\n\tpublic find<This, V2 extends V>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => value is V2,\n\t\tthisArg: This,\n\t): V2 | undefined;\n\tpublic find<This>(fn: (this: This, value: V, key: K, collection: this) => boolean, thisArg: This): V | undefined;\n\tpublic find(fn: (value: V, key: K, collection: this) => boolean, thisArg?: unknown): V | undefined {\n\t\tif (typeof thisArg !== 'undefined') fn = fn.bind(thisArg);\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) return val;\n\t\t}\n\t\treturn undefined;\n\t}\n\n\t/**\n\t * Searches for the key of a single item where the given function returns a truthy value. This behaves like\n\t * [Array.findIndex()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/findIndex),\n\t * but returns the key rather than the positional index.\n\t *\n\t * @param fn The function to test with (should return boolean)\n\t * @param thisArg Value to use as `this` when executing function\n\t *\n\t * @example\n\t * collection.findKey(user => user.username === 'Bob');\n\t */\n\tpublic findKey<K2 extends K>(fn: (value: V, key: K, collection: this) => key is K2): K2 | undefined;\n\tpublic findKey(fn: (value: V, key: K, collection: this) => boolean): K | undefined;\n\tpublic findKey<This, K2 extends K>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => key is K2,\n\t\tthisArg: This,\n\t): K2 | undefined;\n\tpublic findKey<This>(fn: (this: This, value: V, key: K, collection: this) => boolean, thisArg: This): K | undefined;\n\tpublic findKey(fn: (value: V, key: K, collection: this) => boolean, thisArg?: unknown): K | undefined {\n\t\tif (typeof thisArg !== 'undefined') fn = fn.bind(thisArg);\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) return key;\n\t\t}\n\t\treturn undefined;\n\t}\n\n\t/**\n\t * Removes items that satisfy the provided filter function.\n\t *\n\t * @param fn Function used to test (should return a boolean)\n\t * @param thisArg Value to use as `this` when executing function\n\t *\n\t * @returns The number of removed entries\n\t */\n\tpublic sweep(fn: (value: V, key: K, collection: this) => boolean): number;\n\tpublic sweep<T>(fn: (this: T, value: V, key: K, collection: this) => boolean, thisArg: T): number;\n\tpublic sweep(fn: (value: V, key: K, collection: this) => boolean, thisArg?: unknown): number {\n\t\tif (typeof thisArg !== 'undefined') fn = fn.bind(thisArg);\n\t\tconst previousSize = this.size;\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) this.delete(key);\n\t\t}\n\t\treturn previousSize - this.size;\n\t}\n\n\t/**\n\t * Identical to\n\t * [Array.filter()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/filter),\n\t * but returns a Collection instead of an Array.\n\t *\n\t * @param fn The function to test with (should return boolean)\n\t * @param thisArg Value to use as `this` when executing function\n\t *\n\t * @example\n\t * collection.filter(user => user.username === 'Bob');\n\t */\n\tpublic filter<K2 extends K>(fn: (value: V, key: K, collection: this) => key is K2): Collection<K2, V>;\n\tpublic filter<V2 extends V>(fn: (value: V, key: K, collection: this) => value is V2): Collection<K, V2>;\n\tpublic filter(fn: (value: V, key: K, collection: this) => boolean): Collection<K, V>;\n\tpublic filter<This, K2 extends K>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => key is K2,\n\t\tthisArg: This,\n\t): Collection<K2, V>;\n\tpublic filter<This, V2 extends V>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => value is V2,\n\t\tthisArg: This,\n\t): Collection<K, V2>;\n\tpublic filter<This>(fn: (this: This, value: V, key: K, collection: this) => boolean, thisArg: This): Collection<K, V>;\n\tpublic filter(fn: (value: V, key: K, collection: this) => boolean, thisArg?: unknown): Collection<K, V> {\n\t\tif (typeof thisArg !== 'undefined') fn = fn.bind(thisArg);\n\t\tconst results = new this.constructor[Symbol.species]<K, V>();\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) results.set(key, val);\n\t\t}\n\t\treturn results;\n\t}\n\n\t/**\n\t * Partitions the collection into two collections where the first collection\n\t * contains the items that passed and the second contains the items that failed.\n\t *\n\t * @param fn Function used to test (should return a boolean)\n\t * @param thisArg Value to use as `this` when executing function\n\t *\n\t * @example\n\t * const [big, small] = collection.partition(guild => guild.memberCount > 250);\n\t */\n\tpublic partition<K2 extends K>(\n\t\tfn: (value: V, key: K, collection: this) => key is K2,\n\t): [Collection<K2, V>, Collection<Exclude<K, K2>, V>];\n\tpublic partition<V2 extends V>(\n\t\tfn: (value: V, key: K, collection: this) => value is V2,\n\t): [Collection<K, V2>, Collection<K, Exclude<V, V2>>];\n\tpublic partition(fn: (value: V, key: K, collection: this) => boolean): [Collection<K, V>, Collection<K, V>];\n\tpublic partition<This, K2 extends K>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => key is K2,\n\t\tthisArg: This,\n\t): [Collection<K2, V>, Collection<Exclude<K, K2>, V>];\n\tpublic partition<This, V2 extends V>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => value is V2,\n\t\tthisArg: This,\n\t): [Collection<K, V2>, Collection<K, Exclude<V, V2>>];\n\tpublic partition<This>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => boolean,\n\t\tthisArg: This,\n\t): [Collection<K, V>, Collection<K, V>];\n\tpublic partition(\n\t\tfn: (value: V, key: K, collection: this) => boolean,\n\t\tthisArg?: unknown,\n\t): [Collection<K, V>, Collection<K, V>] {\n\t\tif (typeof thisArg !== 'undefined') fn = fn.bind(thisArg);\n\t\tconst results: [Collection<K, V>, Collection<K, V>] = [\n\t\t\tnew this.constructor[Symbol.species]<K, V>(),\n\t\t\tnew this.constructor[Symbol.species]<K, V>(),\n\t\t];\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) {\n\t\t\t\tresults[0].set(key, val);\n\t\t\t} else {\n\t\t\t\tresults[1].set(key, val);\n\t\t\t}\n\t\t}\n\t\treturn results;\n\t}\n\n\t/**\n\t * Maps each item into a Collection, then joins the results into a single Collection. Identical in behavior to\n\t * [Array.flatMap()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/flatMap).\n\t *\n\t * @param fn Function that produces a new Collection\n\t * @param thisArg Value to use as `this` when executing function\n\t *\n\t * @example\n\t * collection.flatMap(guild => guild.members.cache);\n\t */\n\tpublic flatMap<T>(fn: (value: V, key: K, collection: this) => Collection<K, T>): Collection<K, T>;\n\tpublic flatMap<T, This>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => Collection<K, T>,\n\t\tthisArg: This,\n\t): Collection<K, T>;\n\tpublic flatMap<T>(fn: (value: V, key: K, collection: this) => Collection<K, T>, thisArg?: unknown): Collection<K, T> {\n\t\tconst collections = this.map(fn, thisArg);\n\t\treturn new this.constructor[Symbol.species]<K, T>().concat(...collections);\n\t}\n\n\t/**\n\t * Maps each item to another value into an array. Identical in behavior to\n\t * [Array.map()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/map).\n\t *\n\t * @param fn Function that produces an element of the new array, taking three arguments\n\t * @param thisArg Value to use as `this` when executing function\n\t *\n\t * @example\n\t * collection.map(user => user.tag);\n\t */\n\tpublic map<T>(fn: (value: V, key: K, collection: this) => T): T[];\n\tpublic map<This, T>(fn: (this: This, value: V, key: K, collection: this) => T, thisArg: This): T[];\n\tpublic map<T>(fn: (value: V, key: K, collection: this) => T, thisArg?: unknown): T[] {\n\t\tif (typeof thisArg !== 'undefined') fn = fn.bind(thisArg);\n\t\tconst iter = this.entries();\n\t\treturn Array.from({ length: this.size }, (): T => {\n\t\t\tconst [key, value] = iter.next().value;\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n\t\t\treturn fn(value, key, this);\n\t\t});\n\t}\n\n\t/**\n\t * Maps each item to another value into a collection. Identical in behavior to\n\t * [Array.map()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/map).\n\t *\n\t * @param fn Function that produces an element of the new collection, taking three arguments\n\t * @param thisArg Value to use as `this` when executing function\n\t *\n\t * @example\n\t * collection.mapValues(user => user.tag);\n\t */\n\tpublic mapValues<T>(fn: (value: V, key: K, collection: this) => T): Collection<K, T>;\n\tpublic mapValues<This, T>(fn: (this: This, value: V, key: K, collection: this) => T, thisArg: This): Collection<K, T>;\n\tpublic mapValues<T>(fn: (value: V, key: K, collection: this) => T, thisArg?: unknown): Collection<K, T> {\n\t\tif (typeof thisArg !== 'undefined') fn = fn.bind(thisArg);\n\t\tconst coll = new this.constructor[Symbol.species]<K, T>();\n\t\tfor (const [key, val] of this) coll.set(key, fn(val, key, this));\n\t\treturn coll;\n\t}\n\n\t/**\n\t * Checks if there exists an item that passes a test. Identical in behavior to\n\t * [Array.some()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/some).\n\t *\n\t * @param fn Function used to test (should return a boolean)\n\t * @param thisArg Value to use as `this` when executing function\n\t *\n\t * @example\n\t * collection.some(user => user.discriminator === '0000');\n\t */\n\tpublic some(fn: (value: V, key: K, collection: this) => boolean): boolean;\n\tpublic some<T>(fn: (this: T, value: V, key: K, collection: this) => boolean, thisArg: T): boolean;\n\tpublic some(fn: (value: V, key: K, collection: this) => boolean, thisArg?: unknown): boolean {\n\t\tif (typeof thisArg !== 'undefined') fn = fn.bind(thisArg);\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) return true;\n\t\t}\n\t\treturn false;\n\t}\n\n\t/**\n\t * Checks if all items passes a test. Identical in behavior to\n\t * [Array.every()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/every).\n\t *\n\t * @param fn Function used to test (should return a boolean)\n\t * @param thisArg Value to use as `this` when executing function\n\t *\n\t * @example\n\t * collection.every(user => !user.bot);\n\t */\n\tpublic every<K2 extends K>(fn: (value: V, key: K, collection: this) => key is K2): this is Collection<K2, V>;\n\tpublic every<V2 extends V>(fn: (value: V, key: K, collection: this) => value is V2): this is Collection<K, V2>;\n\tpublic every(fn: (value: V, key: K, collection: this) => boolean): boolean;\n\tpublic every<This, K2 extends K>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => key is K2,\n\t\tthisArg: This,\n\t): this is Collection<K2, V>;\n\tpublic every<This, V2 extends V>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => value is V2,\n\t\tthisArg: This,\n\t): this is Collection<K, V2>;\n\tpublic every<This>(fn: (this: This, value: V, key: K, collection: this) => boolean, thisArg: This): boolean;\n\tpublic every(fn: (value: V, key: K, collection: this) => boolean, thisArg?: unknown): boolean {\n\t\tif (typeof thisArg !== 'undefined') fn = fn.bind(thisArg);\n\t\tfor (const [key, val] of this) {\n\t\t\tif (!fn(val, key, this)) return false;\n\t\t}\n\t\treturn true;\n\t}\n\n\t/**\n\t * Applies a function to produce a single value. Identical in behavior to\n\t * [Array.reduce()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/reduce).\n\t *\n\t * @param fn Function used to reduce, taking four arguments; `accumulator`, `currentValue`, `currentKey`,\n\t * and `collection`\n\t * @param initialValue Starting value for the accumulator\n\t *\n\t * @example\n\t * collection.reduce((acc, guild) => acc + guild.memberCount, 0);\n\t */\n\tpublic reduce<T>(fn: (accumulator: T, value: V, key: K, collection: this) => T, initialValue?: T): T {\n\t\tlet accumulator!: T;\n\n\t\tif (typeof initialValue !== 'undefined') {\n\t\t\taccumulator = initialValue;\n\t\t\tfor (const [key, val] of this) accumulator = fn(accumulator, val, key, this);\n\t\t\treturn accumulator;\n\t\t}\n\t\tlet first = true;\n\t\tfor (const [key, val] of this) {\n\t\t\tif (first) {\n\t\t\t\taccumulator = val as unknown as T;\n\t\t\t\tfirst = false;\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\taccumulator = fn(accumulator, val, key, this);\n\t\t}\n\n\t\t// No items iterated.\n\t\tif (first) {\n\t\t\tthrow new TypeError('Reduce of empty collection with no initial value');\n\t\t}\n\n\t\treturn accumulator;\n\t}\n\n\t/**\n\t * Identical to\n\t * [Map.forEach()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map/forEach),\n\t * but returns the collection instead of undefined.\n\t *\n\t * @param fn Function to execute for each element\n\t * @param thisArg Value to use as `this` when executing function\n\t *\n\t * @example\n\t * collection\n\t *  .each(user => console.log(user.username))\n\t *  .filter(user => user.bot)\n\t *  .each(user => console.log(user.username));\n\t */\n\tpublic each(fn: (value: V, key: K, collection: this) => void): this;\n\tpublic each<T>(fn: (this: T, value: V, key: K, collection: this) => void, thisArg: T): this;\n\tpublic each(fn: (value: V, key: K, collection: this) => void, thisArg?: unknown): this {\n\t\tthis.forEach(fn as (value: V, key: K, map: Map<K, V>) => void, thisArg);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Runs a function on the collection and returns the collection.\n\t *\n\t * @param fn Function to execute\n\t * @param thisArg Value to use as `this` when executing function\n\t *\n\t * @example\n\t * collection\n\t *  .tap(coll => console.log(coll.size))\n\t *  .filter(user => user.bot)\n\t *  .tap(coll => console.log(coll.size))\n\t */\n\tpublic tap(fn: (collection: this) => void): this;\n\tpublic tap<T>(fn: (this: T, collection: this) => void, thisArg: T): this;\n\tpublic tap(fn: (collection: this) => void, thisArg?: unknown): this {\n\t\tif (typeof thisArg !== 'undefined') fn = fn.bind(thisArg);\n\t\tfn(this);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Creates an identical shallow copy of this collection.\n\t *\n\t * @example\n\t * const newColl = someColl.clone();\n\t */\n\tpublic clone() {\n\t\treturn new this.constructor[Symbol.species](this);\n\t}\n\n\t/**\n\t * Combines this collection with others into a new collection. None of the source collections are modified.\n\t *\n\t * @param collections Collections to merge\n\t *\n\t * @example\n\t * const newColl = someColl.concat(someOtherColl, anotherColl, ohBoyAColl);\n\t */\n\tpublic concat(...collections: Collection<K, V>[]) {\n\t\tconst newColl = this.clone();\n\t\tfor (const coll of collections) {\n\t\t\tfor (const [key, val] of coll) newColl.set(key, val);\n\t\t}\n\t\treturn newColl;\n\t}\n\n\t/**\n\t * Checks if this collection shares identical items with another.\n\t * This is different to checking for equality using equal-signs, because\n\t * the collections may be different objects, but contain the same data.\n\t *\n\t * @param collection Collection to compare with\n\t *\n\t * @returns Whether the collections have identical contents\n\t */\n\tpublic equals(collection: Collection<K, V>) {\n\t\t// eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n\t\tif (!collection) return false; // runtime check\n\t\tif (this === collection) return true;\n\t\tif (this.size !== collection.size) return false;\n\t\tfor (const [key, value] of this) {\n\t\t\tif (!collection.has(key) || value !== collection.get(key)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\t\treturn true;\n\t}\n\n\t/**\n\t * The sort method sorts the items of a collection in place and returns it.\n\t * The sort is not necessarily stable in Node 10 or older.\n\t * The default sort order is according to string Unicode code points.\n\t *\n\t * @param compareFunction Specifies a function that defines the sort order.\n\t * If omitted, the collection is sorted according to each character's Unicode code point value, according to the string conversion of each element.\n\t *\n\t * @example\n\t * collection.sort((userA, userB) => userA.createdTimestamp - userB.createdTimestamp);\n\t */\n\tpublic sort(compareFunction: Comparator<K, V> = Collection.defaultSort) {\n\t\tconst entries = [...this.entries()];\n\t\tentries.sort((a, b): number => compareFunction(a[1], b[1], a[0], b[0]));\n\n\t\t// Perform clean-up\n\t\tsuper.clear();\n\n\t\t// Set the new entries\n\t\tfor (const [k, v] of entries) {\n\t\t\tsuper.set(k, v);\n\t\t}\n\t\treturn this;\n\t}\n\n\t/**\n\t * The intersect method returns a new structure containing items where the keys are present in both original structures.\n\t *\n\t * @param other The other Collection to filter against\n\t */\n\tpublic intersect(other: Collection<K, V>) {\n\t\tconst coll = new this.constructor[Symbol.species]<K, V>();\n\t\tfor (const [k, v] of other) {\n\t\t\tif (this.has(k)) coll.set(k, v);\n\t\t}\n\t\treturn coll;\n\t}\n\n\t/**\n\t * The difference method returns a new structure containing items where the key is present in one of the original structures but not the other.\n\t *\n\t * @param other The other Collection to filter against\n\t */\n\tpublic difference(other: Collection<K, V>) {\n\t\tconst coll = new this.constructor[Symbol.species]<K, V>();\n\t\tfor (const [k, v] of other) {\n\t\t\tif (!this.has(k)) coll.set(k, v);\n\t\t}\n\t\tfor (const [k, v] of this) {\n\t\t\tif (!other.has(k)) coll.set(k, v);\n\t\t}\n\t\treturn coll;\n\t}\n\n\t/**\n\t * The sorted method sorts the items of a collection and returns it.\n\t * The sort is not necessarily stable in Node 10 or older.\n\t * The default sort order is according to string Unicode code points.\n\t *\n\t * @param compareFunction Specifies a function that defines the sort order.\n\t * If omitted, the collection is sorted according to each character's Unicode code point value,\n\t * according to the string conversion of each element.\n\t *\n\t * @example\n\t * collection.sorted((userA, userB) => userA.createdTimestamp - userB.createdTimestamp);\n\t */\n\tpublic sorted(compareFunction: Comparator<K, V> = Collection.defaultSort) {\n\t\treturn new this.constructor[Symbol.species](this).sort((av, bv, ak, bk) => compareFunction(av, bv, ak, bk));\n\t}\n\n\tpublic toJSON() {\n\t\t// toJSON is called recursively by JSON.stringify.\n\t\treturn [...this.values()];\n\t}\n\n\tprivate static defaultSort<V>(firstValue: V, secondValue: V): number {\n\t\treturn Number(firstValue > secondValue) || Number(firstValue === secondValue) - 1;\n\t}\n}\n\n/**\n * @internal\n */\nexport type Comparator<K, V> = (firstValue: V, secondValue: V, firstKey: K, secondKey: K) => number;\n\nexport default Collection;\n"], "mappings": "ysBAAA,+CAwBO,mBAA+B,IAAU,CAYxC,OAAO,EAAQ,EAA2D,CAChF,GAAI,KAAK,IAAI,GAAM,MAAO,MAAK,IAAI,GACnC,GAAM,GAAe,EAAsB,EAAK,MAChD,YAAK,IAAI,EAAK,GACP,EAUD,UAAU,EAAW,CAC3B,MAAO,GAAK,MAAM,AAAC,GAAM,MAAM,IAAI,IAU7B,UAAU,EAAW,CAC3B,MAAO,GAAK,KAAK,AAAC,GAAM,MAAM,IAAI,IAY5B,MAAM,EAAsC,CAClD,GAAI,MAAO,IAAW,YAAa,MAAO,MAAK,SAAS,OAAO,MAC/D,GAAI,EAAS,EAAG,MAAO,MAAK,KAAK,EAAS,IAC1C,EAAS,KAAK,IAAI,KAAK,KAAM,GAC7B,GAAM,GAAO,KAAK,SAClB,MAAO,OAAM,KAAK,CAAE,OAAQ,GAAU,IAAS,EAAK,OAAO,OAarD,SAAS,EAAsC,CACrD,GAAI,MAAO,IAAW,YAAa,MAAO,MAAK,OAAO,OAAO,MAC7D,GAAI,EAAS,EAAG,MAAO,MAAK,QAAQ,EAAS,IAC7C,EAAS,KAAK,IAAI,KAAK,KAAM,GAC7B,GAAM,GAAO,KAAK,OAClB,MAAO,OAAM,KAAK,CAAE,OAAQ,GAAU,IAAS,EAAK,OAAO,OAarD,KAAK,EAAsC,CACjD,GAAM,GAAM,CAAC,GAAG,KAAK,UACrB,MAAI,OAAO,IAAW,YAAoB,EAAI,EAAI,OAAS,GACvD,EAAS,EAAU,KAAK,MAAM,EAAS,IACtC,EACE,EAAI,MAAM,CAAC,GADE,GAcd,QAAQ,EAAsC,CACpD,GAAM,GAAM,CAAC,GAAG,KAAK,QACrB,MAAI,OAAO,IAAW,YAAoB,EAAI,EAAI,OAAS,GACvD,EAAS,EAAU,KAAK,SAAS,EAAS,IACzC,EACE,EAAI,MAAM,CAAC,GADE,GAWd,GAAG,EAAe,CACxB,SAAQ,KAAK,MAAM,GAEZ,AADK,CAAC,GAAG,KAAK,UACV,GAAG,GAUR,MAAM,EAAe,CAC3B,SAAQ,KAAK,MAAM,GAEZ,AADK,CAAC,GAAG,KAAK,QACV,GAAG,GAYR,OAAO,EAAsC,CACnD,GAAM,GAAM,CAAC,GAAG,KAAK,UACrB,MAAI,OAAO,IAAW,YAAoB,EAAI,KAAK,MAAM,KAAK,SAAW,EAAI,SACzE,CAAC,EAAI,QAAU,CAAC,EAAe,GAC5B,MAAM,KACZ,CAAE,OAAQ,KAAK,IAAI,EAAQ,EAAI,SAC/B,IAAS,EAAI,OAAO,KAAK,MAAM,KAAK,SAAW,EAAI,QAAS,GAAG,IAa1D,UAAU,EAAsC,CACtD,GAAM,GAAM,CAAC,GAAG,KAAK,QACrB,MAAI,OAAO,IAAW,YAAoB,EAAI,KAAK,MAAM,KAAK,SAAW,EAAI,SACzE,CAAC,EAAI,QAAU,CAAC,EAAe,GAC5B,MAAM,KACZ,CAAE,OAAQ,KAAK,IAAI,EAAQ,EAAI,SAC/B,IAAS,EAAI,OAAO,KAAK,MAAM,KAAK,SAAW,EAAI,QAAS,GAAG,IAQ1D,SAAU,CAChB,GAAM,GAAU,CAAC,GAAG,KAAK,WAAW,UACpC,KAAK,QACL,OAAW,CAAC,EAAK,IAAU,GAAS,KAAK,IAAI,EAAK,GAClD,MAAO,MAuBD,KAAK,EAAqD,EAAkC,CAClG,AAAI,MAAO,IAAY,aAAa,GAAK,EAAG,KAAK,IACjD,OAAW,CAAC,EAAK,IAAQ,MACxB,GAAI,EAAG,EAAK,EAAK,MAAO,MAAO,GAuB1B,QAAQ,EAAqD,EAAkC,CACrG,AAAI,MAAO,IAAY,aAAa,GAAK,EAAG,KAAK,IACjD,OAAW,CAAC,EAAK,IAAQ,MACxB,GAAI,EAAG,EAAK,EAAK,MAAO,MAAO,GAe1B,MAAM,EAAqD,EAA2B,CAC5F,AAAI,MAAO,IAAY,aAAa,GAAK,EAAG,KAAK,IACjD,GAAM,GAAe,KAAK,KAC1B,OAAW,CAAC,EAAK,IAAQ,MACxB,AAAI,EAAG,EAAK,EAAK,OAAO,KAAK,OAAO,GAErC,MAAO,GAAe,KAAK,KA0BrB,OAAO,EAAqD,EAAqC,CACvG,AAAI,MAAO,IAAY,aAAa,GAAK,EAAG,KAAK,IACjD,GAAM,GAAU,GAAI,MAAK,YAAY,OAAO,SAC5C,OAAW,CAAC,EAAK,IAAQ,MACxB,AAAI,EAAG,EAAK,EAAK,OAAO,EAAQ,IAAI,EAAK,GAE1C,MAAO,GAgCD,UACN,EACA,EACuC,CACvC,AAAI,MAAO,IAAY,aAAa,GAAK,EAAG,KAAK,IACjD,GAAM,GAAgD,CACrD,GAAI,MAAK,YAAY,OAAO,SAC5B,GAAI,MAAK,YAAY,OAAO,UAE7B,OAAW,CAAC,EAAK,IAAQ,MACxB,AAAI,EAAG,EAAK,EAAK,MAChB,EAAQ,GAAG,IAAI,EAAK,GAEpB,EAAQ,GAAG,IAAI,EAAK,GAGtB,MAAO,GAkBD,QAAW,EAA8D,EAAqC,CACpH,GAAM,GAAc,KAAK,IAAI,EAAI,GACjC,MAAO,IAAI,MAAK,YAAY,OAAO,WAAiB,OAAO,GAAG,GAexD,IAAO,EAA+C,EAAwB,CACpF,AAAI,MAAO,IAAY,aAAa,GAAK,EAAG,KAAK,IACjD,GAAM,GAAO,KAAK,UAClB,MAAO,OAAM,KAAK,CAAE,OAAQ,KAAK,MAAQ,IAAS,CACjD,GAAM,CAAC,EAAK,GAAS,EAAK,OAAO,MAEjC,MAAO,GAAG,EAAO,EAAK,QAgBjB,UAAa,EAA+C,EAAqC,CACvG,AAAI,MAAO,IAAY,aAAa,GAAK,EAAG,KAAK,IACjD,GAAM,GAAO,GAAI,MAAK,YAAY,OAAO,SACzC,OAAW,CAAC,EAAK,IAAQ,MAAM,EAAK,IAAI,EAAK,EAAG,EAAK,EAAK,OAC1D,MAAO,GAeD,KAAK,EAAqD,EAA4B,CAC5F,AAAI,MAAO,IAAY,aAAa,GAAK,EAAG,KAAK,IACjD,OAAW,CAAC,EAAK,IAAQ,MACxB,GAAI,EAAG,EAAK,EAAK,MAAO,MAAO,GAEhC,MAAO,GAyBD,MAAM,EAAqD,EAA4B,CAC7F,AAAI,MAAO,IAAY,aAAa,GAAK,EAAG,KAAK,IACjD,OAAW,CAAC,EAAK,IAAQ,MACxB,GAAI,CAAC,EAAG,EAAK,EAAK,MAAO,MAAO,GAEjC,MAAO,GAcD,OAAU,EAA+D,EAAqB,CACpG,GAAI,GAEJ,GAAI,MAAO,IAAiB,YAAa,CACxC,EAAc,EACd,OAAW,CAAC,EAAK,IAAQ,MAAM,EAAc,EAAG,EAAa,EAAK,EAAK,MACvE,MAAO,GAER,GAAI,GAAQ,GACZ,OAAW,CAAC,EAAK,IAAQ,MAAM,CAC9B,GAAI,EAAO,CACV,EAAc,EACd,EAAQ,GACR,SAED,EAAc,EAAG,EAAa,EAAK,EAAK,MAIzC,GAAI,EACH,KAAM,IAAI,WAAU,oDAGrB,MAAO,GAmBD,KAAK,EAAkD,EAAyB,CACtF,YAAK,QAAQ,EAAkD,GACxD,KAiBD,IAAI,EAAgC,EAAyB,CACnE,MAAI,OAAO,IAAY,aAAa,GAAK,EAAG,KAAK,IACjD,EAAG,MACI,KASD,OAAQ,CACd,MAAO,IAAI,MAAK,YAAY,OAAO,SAAS,MAWtC,UAAU,EAAiC,CACjD,GAAM,GAAU,KAAK,QACrB,OAAW,KAAQ,GAClB,OAAW,CAAC,EAAK,IAAQ,GAAM,EAAQ,IAAI,EAAK,GAEjD,MAAO,GAYD,OAAO,EAA8B,CAE3C,GAAI,CAAC,EAAY,MAAO,GACxB,GAAI,OAAS,EAAY,MAAO,GAChC,GAAI,KAAK,OAAS,EAAW,KAAM,MAAO,GAC1C,OAAW,CAAC,EAAK,IAAU,MAC1B,GAAI,CAAC,EAAW,IAAI,IAAQ,IAAU,EAAW,IAAI,GACpD,MAAO,GAGT,MAAO,GAcD,KAAK,EAAoC,EAAW,YAAa,CACvE,GAAM,GAAU,CAAC,GAAG,KAAK,WACzB,EAAQ,KAAK,CAAC,EAAG,IAAc,EAAgB,EAAE,GAAI,EAAE,GAAI,EAAE,GAAI,EAAE,KAGnE,MAAM,QAGN,OAAW,CAAC,EAAG,IAAM,GACpB,MAAM,IAAI,EAAG,GAEd,MAAO,MAQD,UAAU,EAAyB,CACzC,GAAM,GAAO,GAAI,MAAK,YAAY,OAAO,SACzC,OAAW,CAAC,EAAG,IAAM,GACpB,AAAI,KAAK,IAAI,IAAI,EAAK,IAAI,EAAG,GAE9B,MAAO,GAQD,WAAW,EAAyB,CAC1C,GAAM,GAAO,GAAI,MAAK,YAAY,OAAO,SACzC,OAAW,CAAC,EAAG,IAAM,GACpB,AAAK,KAAK,IAAI,IAAI,EAAK,IAAI,EAAG,GAE/B,OAAW,CAAC,EAAG,IAAM,MACpB,AAAK,EAAM,IAAI,IAAI,EAAK,IAAI,EAAG,GAEhC,MAAO,GAeD,OAAO,EAAoC,EAAW,YAAa,CACzE,MAAO,IAAI,MAAK,YAAY,OAAO,SAAS,MAAM,KAAK,CAAC,EAAI,EAAI,EAAI,IAAO,EAAgB,EAAI,EAAI,EAAI,IAGjG,QAAS,CAEf,MAAO,CAAC,GAAG,KAAK,gBAGF,aAAe,EAAe,EAAwB,CACpE,MAAO,QAAO,EAAa,IAAgB,OAAO,IAAe,GAAe,IA7oB3E,sBACiB,EADjB,EACiB,UAA6B,GAqpBrD,GAAO,GAAQ", "names": []}