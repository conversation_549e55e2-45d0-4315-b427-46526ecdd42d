{"cannot_goback": "Aucune position récente trouvée. V<PERSON> devez d'abord vous téléporter", "no_marker": "Vous n'avez défini aucun marqueur", "command_tpm": "%s Se téléporter au marqueur", "command_noclip": "%s Toggle <PERSON>", "command_openui": "%s <PERSON><PERSON><PERSON><PERSON><PERSON>", "command_weather_notfound": "Vous essayez d'appliquer une météo qui n'existe pas: %s", "teleport_invalid_coords": "Vous essayez de vous téléporter sur des coords invalides", "model_doesnt_exist": "Le model %s n'existe pas", "copied_coords_clipboard": "Coordonnées copiée dans le presse-papier", "copied_model_clipboard": "Model hash copied to clipboard", "press_escape_exit": "Appuyer sur 'Echap' pour sortir du mode d'édition", "custom_location_created": "Posiiton custom sauvegardée", "vehicle_upgraded": "Véhicule amélioré", "weapon_gave": "Vous avez reçu une arme", "weapon_cant_carry": "Vous ne pouvez pas porter cette arme", "max_health_set": "Votre vie a été restaurée", "entity_cant_be_loaded": "L'entité n'a pas pu être chargée...", "entity_doesnt_exist": "L'entité/model n'existe pas", "entity_deleted": "Entité supprimée", "teleport_success": "Téléportation effectuée. Utilisez '/goback' pour revenir à la dernière position", "ui_home": "Accueil", "ui_world": "Environnement", "ui_exit": "<PERSON><PERSON><PERSON>", "ui_copy_coords": "<PERSON><PERSON>r coords", "ui_copied_coords": "Coords copiées", "ui_copy_name": "Copier nom", "ui_copied_name": "Coords nom", "ui_copy_hash": "Copier hash", "ui_copied_hash": "Coords hash", "ui_name": "Nom", "ui_hash": "Hash", "ui_coords": "<PERSON><PERSON><PERSON>", "ui_heading": "Direction", "ui_interior_id": "ID de l'intérieur", "ui_current_room": "Room actuelle", "ui_teleport": "Téléporter", "ui_not_in_interior": "Vous n'êtes pas dans un intérieur", "ui_no_last_location": "Vous ne vous êtes pas encore téléporté", "ui_current_coords": "Coords actuelles", "ui_set_coords": "Appliquer coords", "ui_save_location": "Sauver la position", "ui_last_location": "Dernière Position", "ui_current_interior": "Intérieur actuel", "ui_quick_actions": "Actions Rapides", "ui_clean_zone": "Nettoyer la zone", "ui_clean_ped": "Net<PERSON>yer le ped", "ui_upgrade_vehicle": "Booster véhicule", "ui_repair_vehicle": "Fix véhicule", "ui_delete_vehicle": "Retirer véhicule", "ui_set_sunny_day": "Beau temps", "ui_spawn_vehicle": "Spawn véhicule", "ui_max_health": "Vie max", "ui_time_freeze": "Temps figé", "ui_time_not_freeze": "Temps non figé", "ui_time": "<PERSON><PERSON>", "ui_sync": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ui_freeze_time": "Figer l'heure", "ui_weather": "<PERSON><PERSON><PERSON><PERSON>", "ui_choose_weather": "Choisissez la météo que vous souhaitez", "ui_current_weather": "Météo actuelle ?", "ui_freeze_weather": "Figer la météo", "ui_interior": "Intérieur", "ui_room_count": "Nombre de room", "ui_portal_count": "Nombre de portails", "ui_portals": "Portails", "ui_infos": "Infos", "ui_fill_portals": "<PERSON><PERSON><PERSON><PERSON>", "ui_outline_portals": "Contours", "ui_corcers_portals": "Coins", "ui_flag": "Flag", "ui_room_from": "Depuis la room", "ui_room_to": "Vers la room", "ui_index": "Index", "ui_timecycle": "Timecycle", "ui_no_timecycle_found": "Aucun timecycle trouvé", "ui_object_spawner": "Spawner d'objet", "ui_locations": "Positions", "ui_snap_to_ground": "Plaquer au sol", "ui_duplicate": "<PERSON><PERSON><PERSON><PERSON>", "ui_no_location_found": "Aucune position trouvée", "ui_goto": "Téléporter", "ui_show_custom_locations": "<PERSON><PERSON><PERSON><PERSON> positions customs", "ui_show_vanilla_locations": "Afficher positions vanilla", "ui_create_custom_location": "<PERSON><PERSON><PERSON> une position custom", "ui_search": "Recherche", "ui_rename": "<PERSON>mmer", "ui_delete": "<PERSON><PERSON><PERSON><PERSON>", "ui_vanilla": "Vanilla", "ui_custom": "Custom", "ui_peds": "Peds", "ui_no_ped_found": "<PERSON><PERSON><PERSON> ped trouvé", "ui_set_by_name": "Appliquer par nom", "ui_set_ped": "Appliquer", "ui_vehicles": "Véhicules", "ui_spawn": "Spawn", "ui_spawn_by_name": "Spawn par nom", "ui_no_vehicle_found": "Aucun véhicule trouvé", "ui_weapons": "Armes", "ui_give_weapon_by_name": "Donner l'arme par nom", "ui_give_weapon": "Donner l'arme", "ui_no_weapon_found": "Aucune arme trouvée", "ui_set_coords_as_string": "Appliquer via texte", "ui_set_coords_separate": "Appliquer séparemment", "ui_confirm": "Confirmer", "ui_cancel": "Annuler", "ui_location_name": "Nom de la position", "ui_create_location_description": "Sauvegarder vos coordonnées et heading actuels", "ui_add_entity": "Ajouter une nouvelle entité", "ui_add_entity_description": "Entrer le nom du model", "ui_delete_all_entities": "Supprimer tout les objets ajoutés ?", "ui_amount": "Quantité", "ui_portal_flag_1": "Désactive le rendu extérieur", "ui_portal_flag_2": "Désactive le rendu intérieur", "ui_portal_flag_4": "Miroir", "ui_portal_flag_8": "Extra luminosité", "ui_portal_flag_16": "Inconnu 16", "ui_portal_flag_32": "Utilise les LOD à l'exterieur", "ui_portal_flag_64": "Désactive le portail quand la porte est fermée", "ui_portal_flag_128": "Inconnu 128", "ui_portal_flag_256": "Active les portails exterieurs dans le mirroir", "ui_portal_flag_512": "Inconnu 512", "ui_portal_flag_1024": "Active les entités du limbo dans le mirroir", "ui_portal_flag_2048": "Inconnu 2048", "ui_portal_flag_4096": "Inconnu 4096", "ui_portal_flag_8192": "<PERSON><PERSON><PERSON> le 'farclipping'", "ui_update_warning": "Mise à jour disponible !", "ui_github": "<PERSON><PERSON><PERSON><PERSON><PERSON> le d<PERSON>", "ui_discord": "Rejoindre le discord de Dolu", "ui_audio": "Audio", "ui_static_emitters": "Static Emitters", "ui_draw_static_emitters": "Afficher les static emitters", "ui_draw_distance": "Distance d'affichage", "ui_closest_emitter_info": "Emitter le plus proche", "ui_refresh": "<PERSON><PERSON><PERSON><PERSON>", "ui_distance": "Distance", "ui_meters": "mètres", "ui_flags": "Flags", "ui_room": "Room", "ui_radio_station": "Station de radio", "ui_copied_rotation": "Rotation copiée", "ui_copy_rotation": "Copier rotation"}