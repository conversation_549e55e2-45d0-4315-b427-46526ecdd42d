<div align="center">
  <br />
  <p>
    <a href="https://discord.js.org"><img src="https://discord.js.org/static/logo.svg" width="546" alt="discord.js" /></a>
  </p>
  <br />
  <p>
    <a href="https://discord.gg/djs"><img src="https://img.shields.io/discord/222078108977594368?color=5865F2&logo=discord&logoColor=white" alt="Discord server" /></a>
    <a href="https://www.npmjs.com/package/@discordjs/collection"><img src="https://img.shields.io/npm/v/@discordjs/collection.svg?maxAge=3600" alt="npm version" /></a>
    <a href="https://www.npmjs.com/package/@discordjs/collection"><img src="https://img.shields.io/npm/dt/@discordjs/collection.svg?maxAge=3600" alt="npm downloads" /></a>
    <a href="https://github.com/discordjs/collection/actions"><img src="https://github.com/discordjs/collection/workflows/Tests/badge.svg" alt="Build status" /></a>
  </p>
</div>

## About

`@discordjs/collection` is a powerful utility data structure used in discord.js.

## Installation

**Node.js 16.0.0 or newer is required.**

```sh-session
npm install @discordjs/collection
yarn add @discordjs/collection
pnpm add @discordjs/collection
```

## Links

- [Website](https://discord.js.org/) ([source](https://github.com/discordjs/website))
- [Documentation](https://discord.js.org/#/docs/collection)
- [discord.js Discord server](https://discord.gg/djs)
- [GitHub](https://github.com/discordjs/collection)
- [npm](https://www.npmjs.com/package/@discordjs/collection)

## Contributing

Before creating an issue, please ensure that it hasn't already been reported/suggested, and double-check the
[documentation](https://discord.js.org/#/docs/collection).  
See [the contribution guide](https://github.com/discordjs/collection/blob/main/.github/CONTRIBUTING.md) if you'd like to submit a PR.

## Help

If you don't understand something in the documentation, you are experiencing problems, or you just need a gentle
nudge in the right direction, please don't hesitate to join our official [discord.js Server](https://discord.gg/djs).
