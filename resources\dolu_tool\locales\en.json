{"cannot_goback": "No last location found. You need to teleport somewhere first", "no_marker": "You did not set any marker", "command_tpm": "%s Teleport to marker", "command_noclip": "%s Toggle noclip", "command_openui": "%s Open Dolu Tool", "command_weather_notfound": "Trying to set an invalid weather type: %s", "teleport_invalid_coords": "Trying to teleport player to invalid coords type", "model_doesnt_exist": "Model %s does not exists", "copied_coords_clipboard": "Coords copied to clipboard", "copied_model_clipboard": "Model hash copied to clipboard", "press_escape_exit": "Press 'Escape' to exit edit mode", "custom_location_created": "Custom location succefully created", "vehicle_upgraded": "Vehicle succefully upgraded!", "weapon_gave": "You just receive a weapon", "weapon_cant_carry": "You cannot receive this weapon", "max_health_set": "Max health succefully set", "entity_cant_be_loaded": "The entity cannot be loaded...", "entity_doesnt_exist": "Entity does not exist", "entity_deleted": "Entity succefully deleted", "teleport_success": "Succ<PERSON><PERSON> teleported. Use /goback to go back to last location", "ui_home": "Home", "ui_world": "World", "ui_exit": "Exit", "ui_copy_coords": "Copy coords", "ui_copied_coords": "Copied coords", "ui_copy_name": "Copy name", "ui_copied_name": "Copied name", "ui_copy_hash": "Copy hash", "ui_copied_hash": "Copied hash", "ui_name": "Name", "ui_hash": "Hash", "ui_coords": "<PERSON><PERSON><PERSON>", "ui_heading": "Heading", "ui_interior_id": "Interior ID", "ui_current_room": "Current Room", "ui_teleport": "Teleport", "ui_not_in_interior": "You are not inside any interior", "ui_no_last_location": "You did not teleport to any location yet", "ui_current_coords": "Current Coords", "ui_set_coords": "Set coords", "ui_save_location": "Save as location", "ui_last_location": "Last Location", "ui_current_interior": "Current Interior", "ui_quick_actions": "Quick Actions", "ui_clean_zone": "Clean zone", "ui_clean_ped": "Clean ped", "ui_upgrade_vehicle": "Upgrade vehicle", "ui_repair_vehicle": "Repair vehicle", "ui_delete_vehicle": "Delete vehicle", "ui_set_sunny_day": "Set sunny day", "ui_spawn_vehicle": "Spawn vehicle", "ui_max_health": "Max health", "ui_time_freeze": "Time frozen", "ui_time_not_freeze": "Time not frozen", "ui_time": "Time", "ui_sync": "Sync", "ui_freeze_time": "Freeze time", "ui_weather": "Weather", "ui_choose_weather": "Pick a weather type", "ui_current_weather": "Current weather?", "ui_freeze_weather": "Freeze weather", "ui_interior": "Interior", "ui_room_count": "Room count", "ui_portal_count": "Portal count", "ui_portals": "Portals", "ui_infos": "Infos", "ui_fill_portals": "Fill", "ui_outline_portals": "Outline", "ui_corcers_portals": "Corners", "ui_flag": "Flag", "ui_room_from": "Room from", "ui_room_to": "Room to", "ui_index": "Index", "ui_timecycle": "Timecycle", "ui_no_timecycle_found": "No timecycle found", "ui_object_spawner": "Object Spawner", "ui_locations": "Locations", "ui_snap_to_ground": "Snap to ground", "ui_duplicate": "Duplicate", "ui_no_location_found": "No location found", "ui_goto": "Go to", "ui_show_custom_locations": "Show custom locations", "ui_show_vanilla_locations": "Show vanilla locations", "ui_create_custom_location": "Create custom location", "ui_search": "Search", "ui_rename": "<PERSON><PERSON>", "ui_delete": "Delete", "ui_vanilla": "Vanilla", "ui_custom": "Custom", "ui_peds": "Peds", "ui_no_ped_found": "No ped found", "ui_set_by_name": "Set by name", "ui_set_ped": "Apply", "ui_vehicles": "Vehicles", "ui_spawn": "Spawn", "ui_spawn_by_name": "Spawn by name", "ui_no_vehicle_found": "No vehicle found", "ui_weapons": "Weapons", "ui_give_weapon_by_name": "Give weapon by name", "ui_give_weapon": "Give", "ui_no_weapon_found": "No weapon found", "ui_set_coords_as_string": "Set as string", "ui_set_coords_separate": "Set as separated values", "ui_confirm": "Confirm", "ui_cancel": "Cancel", "ui_location_name": "Location name", "ui_create_location_description": "Will save your current coords and heading", "ui_add_entity": "Add a new entity", "ui_add_entity_description": "Enter the name of the entity", "ui_delete_all_entities": "Delete all spawned entities?", "ui_amount": "Amount", "ui_portal_flag_1": "Disables exterior rendering", "ui_portal_flag_2": "Disables interior rendering", "ui_portal_flag_4": "Mirror", "ui_portal_flag_8": "Extra bloom", "ui_portal_flag_16": "Unknown 16", "ui_portal_flag_32": "Use exterior LOD", "ui_portal_flag_64": "Hide when door closed", "ui_portal_flag_128": "Unknown 128", "ui_portal_flag_256": "Mirror exterior portals", "ui_portal_flag_512": "Unknown 512", "ui_portal_flag_1024": "Mirror limbo entities", "ui_portal_flag_2048": "Unknown 2048", "ui_portal_flag_4096": "Unknown 4096", "ui_portal_flag_8192": "Disable farclipping", "ui_update_warning": "Update available!", "ui_github": "Open Github Repository", "ui_discord": "Join <PERSON>", "ui_audio": "Audio", "ui_static_emitters": "Static Emitters", "ui_draw_static_emitters": "Show static emitters", "ui_draw_distance": "Draw Distance", "ui_closest_emitter_info": "Closest Emitter Info", "ui_refresh": "Refresh", "ui_distance": "Distance", "ui_meters": "meters", "ui_flags": "Flags", "ui_room": "Room", "ui_radio_station": "Radio Station", "ui_copied_rotation": "Copied rotation", "ui_copy_rotation": "Copy rotation"}