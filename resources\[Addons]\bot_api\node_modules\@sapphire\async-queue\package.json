{"name": "@sapphire/async-queue", "version": "1.1.9", "description": "Sequential asynchronous lock-based queue for promises", "author": "@sapphire", "license": "MIT", "main": "dist/index.js", "module": "dist/index.mjs", "browser": "dist/index.umd.js", "unpkg": "dist/index.umd.js", "types": "dist/index.d.ts", "typedocMain": "src/index.ts", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "sideEffects": false, "homepage": "https://github.com/sapphiredev/utilities/tree/main/packages/async-queue", "scripts": {"test": "jest", "lint": "eslint src tests --ext ts --fix -c ../../.eslintrc", "build": "rollup -c", "start": "yarn build -w", "prepublishOnly": "yarn build"}, "repository": {"type": "git", "url": "git+https://github.com/sapphiredev/utilities.git", "directory": "packages/async-queue"}, "files": ["dist", "!dist/*.tsbuildinfo"], "engines": {"node": ">=v14.0.0", "npm": ">=7.0.0"}, "keywords": ["@sapphire/async-queue", "bot", "typescript", "ts", "yarn", "discord", "sapphire", "standalone"], "bugs": {"url": "https://github.com/sapphiredev/utilities/issues"}, "publishConfig": {"access": "public"}, "gitHead": "4889ea35a0b081021981ff3d73e5005dd1e922ce"}