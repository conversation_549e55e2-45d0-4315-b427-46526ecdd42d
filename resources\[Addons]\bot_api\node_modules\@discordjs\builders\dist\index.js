var Ge=Object.create;var O=Object.defineProperty;var he=Object.getOwnPropertyDescriptor;var qe=Object.getOwnPropertyNames;var ze=Object.getPrototypeOf,We=Object.prototype.hasOwnProperty;var je=(t,e,i)=>e in t?O(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i;var Ce=t=>O(t,"__esModule",{value:!0});var w=(t,e)=>{for(var i in e)O(t,i,{get:e[i],enumerable:!0})},fe=(t,e,i,o)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of qe(e))!We.call(t,r)&&(i||r!=="default")&&O(t,r,{get:()=>e[r],enumerable:!(o=he(e,r))||o.enumerable});return t},Ze=(t,e)=>fe(Ce(O(t!=null?Ge(ze(t)):{},"default",!e&&t&&t.__esModule?{get:()=>t.default,enumerable:!0}:{value:t,enumerable:!0})),t),Ke=(t=>(e,i)=>t&&t.get(e)||(i=fe(Ce({}),e,1),t&&t.set(e,i),i))(typeof WeakMap!="undefined"?new WeakMap:0),p=(t,e,i,o)=>{for(var r=o>1?void 0:o?he(e,i):e,A=t.length-1,R;A>=0;A--)(R=t[A])&&(r=(o?R(e,i,r):R(r))||r);return o&&r&&O(e,i,r),r};var n=(t,e,i)=>(je(t,typeof e!="symbol"?e+"":e,i),i);var yt={};w(yt,{ContextMenuCommandAssertions:()=>ue,ContextMenuCommandBuilder:()=>Je,Embed:()=>M,EmbedAssertions:()=>ie,Faces:()=>Ae,SlashCommandAssertions:()=>re,SlashCommandBooleanOption:()=>J,SlashCommandBuilder:()=>j,SlashCommandChannelOption:()=>y,SlashCommandIntegerOption:()=>P,SlashCommandMentionableOption:()=>q,SlashCommandNumberOption:()=>T,SlashCommandRoleOption:()=>z,SlashCommandStringOption:()=>I,SlashCommandSubcommandBuilder:()=>c,SlashCommandSubcommandGroupBuilder:()=>x,SlashCommandUserOption:()=>W,TimestampStyles:()=>ut,blockQuote:()=>nt,bold:()=>Ye,channelMention:()=>mt,codeBlock:()=>Qe,formatEmoji:()=>dt,hideLinkEmbed:()=>ot,hyperlink:()=>rt,inlineCode:()=>He,italic:()=>Xe,memberNicknameMention:()=>pt,quote:()=>it,roleMention:()=>lt,spoiler:()=>at,strikethrough:()=>tt,time:()=>ct,underscore:()=>et,userMention:()=>st});var ie={};w(ie,{authorNamePredicate:()=>Q,colorPredicate:()=>H,descriptionPredicate:()=>X,embedFieldPredicate:()=>be,embedFieldsArrayPredicate:()=>_,fieldInlinePredicate:()=>k,fieldLengthPredicate:()=>xe,fieldNamePredicate:()=>S,fieldValuePredicate:()=>E,footerTextPredicate:()=>Y,timestampPredicate:()=>ee,titlePredicate:()=>te,urlPredicate:()=>u,validateFieldLength:()=>L});var m=require("zod"),S=m.z.string().min(1).max(256),E=m.z.string().min(1).max(1024),k=m.z.boolean().optional(),be=m.z.object({name:S,value:E,inline:k}),_=be.array(),xe=m.z.number().lte(25);function L(t,e){xe.parse(t.length+e)}var Q=S.nullable(),u=m.z.string().url().nullish(),H=m.z.number().gte(0).lte(16777215).nullable(),X=m.z.string().min(1).max(4096).nullable(),Y=m.z.string().min(1).max(2048).nullable(),ee=m.z.union([m.z.number(),m.z.date()]).nullable(),te=S.nullable();var M=class{constructor(e={}){n(this,"fields");n(this,"title");n(this,"description");n(this,"url");n(this,"color");n(this,"timestamp");n(this,"thumbnail");n(this,"image");n(this,"video");n(this,"author");n(this,"provider");n(this,"footer");this.title=e.title,this.description=e.description,this.url=e.url,this.color=e.color,this.thumbnail=e.thumbnail,this.image=e.image,this.video=e.video,this.author=e.author,this.provider=e.provider,this.footer=e.footer,this.fields=e.fields??[],e.timestamp&&(this.timestamp=new Date(e.timestamp).toISOString())}get length(){return(this.title?.length??0)+(this.description?.length??0)+this.fields.reduce((e,i)=>e+i.name.length+i.value.length,0)+(this.footer?.text.length??0)+(this.author?.name.length??0)}addField(e){return this.addFields(e)}addFields(...e){return _.parse(e),L(this.fields,e.length),this.fields.push(...M.normalizeFields(...e)),this}spliceFields(e,i,...o){return _.parse(o),L(this.fields,o.length-i),this.fields.splice(e,i,...M.normalizeFields(...o)),this}setAuthor(e){if(e===null)return this.author=void 0,this;let{name:i,iconURL:o,url:r}=e;return Q.parse(i),u.parse(o),u.parse(r),this.author={name:i,url:r,icon_url:o},this}setColor(e){return H.parse(e),this.color=e??void 0,this}setDescription(e){return X.parse(e),this.description=e??void 0,this}setFooter(e){if(e===null)return this.footer=void 0,this;let{text:i,iconURL:o}=e;return Y.parse(i),u.parse(o),this.footer={text:i,icon_url:o},this}setImage(e){return u.parse(e),this.image=e?{url:e}:void 0,this}setThumbnail(e){return u.parse(e),this.thumbnail=e?{url:e}:void 0,this}setTimestamp(e=Date.now()){return ee.parse(e),this.timestamp=e?new Date(e).toISOString():void 0,this}setTitle(e){return te.parse(e),this.title=e??void 0,this}setURL(e){return u.parse(e),this.url=e??void 0,this}toJSON(){return{...this}}static normalizeFields(...e){return e.flat(1/0).map(i=>(S.parse(i.name),E.parse(i.value),k.parse(i.inline),{name:i.name,value:i.value,inline:i.inline??void 0}))}};function Qe(t,e){return typeof e=="undefined"?`\`\`\`
${t}\`\`\``:`\`\`\`${t}
${e}\`\`\``}function He(t){return`\`${t}\``}function Xe(t){return`_${t}_`}function Ye(t){return`**${t}**`}function et(t){return`__${t}__`}function tt(t){return`~~${t}~~`}function it(t){return`> ${t}`}function nt(t){return`>>> ${t}`}function ot(t){return`<${t}>`}function rt(t,e,i){return i?`[${t}](${e} "${i}")`:`[${t}](${e})`}function at(t){return`||${t}||`}function st(t){return`<@${t}>`}function pt(t){return`<@!${t}>`}function mt(t){return`<#${t}>`}function lt(t){return`<@&${t}>`}function dt(t,e=!1){return`<${e?"a":""}:_:${t}>`}function ct(t,e){return typeof t!="number"&&(t=Math.floor((t?.getTime()??Date.now())/1e3)),typeof e=="string"?`<t:${t}:${e}>`:`<t:${t}>`}var ut={ShortTime:"t",LongTime:"T",ShortDate:"d",LongDate:"D",ShortDateTime:"f",LongDateTime:"F",RelativeTime:"R"},Ae=(o=>(o.Shrug="\xAF\\_(\u30C4)\\_/\xAF",o.Tableflip="(\u256F\xB0\u25A1\xB0\uFF09\u256F\uFE35 \u253B\u2501\u253B",o.Unflip="\u252C\u2500\u252C \u30CE( \u309C-\u309C\u30CE)",o))(Ae||{});var re={};w(re,{assertReturnOfBuilder:()=>C,validateDefaultPermission:()=>ne,validateDescription:()=>D,validateMaxChoicesLength:()=>oe,validateMaxOptionsLength:()=>d,validateName:()=>U,validateRequired:()=>F,validateRequiredParameters:()=>h});var V=Ze(require("@sindresorhus/is")),v=require("zod");function h(t,e,i){U(t),D(e),d(i)}var ht=v.z.string().min(1).max(32).regex(/^[\P{Lu}\p{N}_-]+$/u);function U(t){ht.parse(t)}var Ct=v.z.string().min(1).max(100);function D(t){Ct.parse(t)}var Oe=v.z.boolean();function ne(t){Oe.parse(t)}function F(t){Oe.parse(t)}var Se=v.z.unknown().array().max(25);function d(t){Se.parse(t)}function oe(t){Se.parse(t)}function C(t,e){let i=e.name;if(V.default.nullOrUndefined(t))throw new TypeError(`Expected to receive a ${i} builder, got ${t===null?"null":"undefined"} instead.`);if(V.default.primitive(t))throw new TypeError(`Expected to receive a ${i} builder, got a primitive (${typeof t}) instead.`);if(!(t instanceof e)){let o=t,r=V.default.function_(t)?t.name:o.constructor.name,A=Reflect.get(o,Symbol.toStringTag),R=A?`${r} [${A}]`:r;throw new TypeError(`Expected to receive a ${i} builder, got ${R} instead.`)}}var Fe=require("ts-mixer");var ye=require("discord-api-types/v9");var f=class{constructor(){n(this,"name");n(this,"description")}setName(e){return U(e),Reflect.set(this,"name",e),this}setDescription(e){return D(e),Reflect.set(this,"description",e),this}};var s=class extends f{constructor(){super(...arguments);n(this,"required",!1)}setRequired(e){return F(e),Reflect.set(this,"required",e),this}runRequiredValidations(){h(this.name,this.description,[]),F(this.required)}};var J=class extends s{constructor(){super(...arguments);n(this,"type",ye.ApplicationCommandOptionType.Boolean)}toJSON(){return this.runRequiredValidations(),{...this}}};var ge=require("discord-api-types/v9"),Pe=require("ts-mixer");var l=require("discord-api-types/v9"),ae=require("zod"),ft=[l.ChannelType.GuildText,l.ChannelType.GuildVoice,l.ChannelType.GuildCategory,l.ChannelType.GuildNews,l.ChannelType.GuildStore,l.ChannelType.GuildNewsThread,l.ChannelType.GuildPublicThread,l.ChannelType.GuildPrivateThread,l.ChannelType.GuildStageVoice],bt=ae.z.union(ft.map(t=>ae.z.literal(t))),se=class{constructor(){n(this,"channel_types")}addChannelType(e){return this.channel_types===void 0&&Reflect.set(this,"channel_types",[]),bt.parse(e),this.channel_types.push(e),this}addChannelTypes(e){return e.forEach(i=>this.addChannelType(i)),this}};var y=class extends s{constructor(){super(...arguments);n(this,"type",ge.ApplicationCommandOptionType.Channel)}toJSON(){return this.runRequiredValidations(),{...this}}};y=p([(0,Pe.mix)(se)],y);var Re=require("discord-api-types/v9"),Me=require("ts-mixer"),ve=require("zod");var N=class{constructor(){n(this,"maxValue");n(this,"minValue")}};var Te=require("discord-api-types/v9"),g=require("zod");var G=g.z.string().min(1).max(100),Ie=g.z.number().gt(-1/0).lt(1/0),$e=g.z.tuple([G,g.z.union([G,Ie])]).array(),xt=g.z.boolean(),b=class{constructor(){n(this,"choices");n(this,"autocomplete");n(this,"type")}addChoice(e,i){if(this.autocomplete)throw new RangeError("Autocomplete and choices are mutually exclusive to each other.");return this.choices===void 0&&Reflect.set(this,"choices",[]),oe(this.choices),G.parse(e),this.type===Te.ApplicationCommandOptionType.String?G.parse(i):Ie.parse(i),this.choices.push({name:e,value:i}),this}addChoices(e){if(this.autocomplete)throw new RangeError("Autocomplete and choices are mutually exclusive to each other.");$e.parse(e);for(let[i,o]of e)this.addChoice(i,o);return this}setChoices(e){if(e.length>0&&this.autocomplete)throw new RangeError("Autocomplete and choices are mutually exclusive to each other.");$e.parse(e),Reflect.set(this,"choices",[]);for(let[i,o]of e)this.addChoice(i,o);return this}setAutocomplete(e){if(xt.parse(e),e&&Array.isArray(this.choices)&&this.choices.length>0)throw new RangeError("Autocomplete and choices are mutually exclusive to each other.");return Reflect.set(this,"autocomplete",e),this}};var Ne=ve.z.number().int().nonnegative(),P=class extends s{constructor(){super(...arguments);n(this,"type",Re.ApplicationCommandOptionType.Integer)}setMaxValue(e){return Ne.parse(e),Reflect.set(this,"maxValue",e),this}setMinValue(e){return Ne.parse(e),Reflect.set(this,"minValue",e),this}toJSON(){if(this.runRequiredValidations(),this.autocomplete&&Array.isArray(this.choices)&&this.choices.length>0)throw new RangeError("Autocomplete and choices are mutually exclusive to each other.");return{...this}}};P=p([(0,Me.mix)(N,b)],P);var Be=require("discord-api-types/v9");var q=class extends s{constructor(){super(...arguments);n(this,"type",Be.ApplicationCommandOptionType.Mentionable)}toJSON(){return this.runRequiredValidations(),{...this}}};var we=require("discord-api-types/v9"),Ee=require("ts-mixer"),ke=require("zod");var _e=ke.z.number().nonnegative(),T=class extends s{constructor(){super(...arguments);n(this,"type",we.ApplicationCommandOptionType.Number)}setMaxValue(e){return _e.parse(e),Reflect.set(this,"maxValue",e),this}setMinValue(e){return _e.parse(e),Reflect.set(this,"minValue",e),this}toJSON(){if(this.runRequiredValidations(),this.autocomplete&&Array.isArray(this.choices)&&this.choices.length>0)throw new RangeError("Autocomplete and choices are mutually exclusive to each other.");return{...this}}};T=p([(0,Ee.mix)(N,b)],T);var Le=require("discord-api-types/v9");var z=class extends s{constructor(){super(...arguments);n(this,"type",Le.ApplicationCommandOptionType.Role)}toJSON(){return this.runRequiredValidations(),{...this}}};var Ve=require("discord-api-types/v9"),Ue=require("ts-mixer");var I=class extends s{constructor(){super(...arguments);n(this,"type",Ve.ApplicationCommandOptionType.String)}toJSON(){if(this.runRequiredValidations(),this.autocomplete&&Array.isArray(this.choices)&&this.choices.length>0)throw new RangeError("Autocomplete and choices are mutually exclusive to each other.");return{...this}}};I=p([(0,Ue.mix)(b)],I);var De=require("discord-api-types/v9");var W=class extends s{constructor(){super(...arguments);n(this,"type",De.ApplicationCommandOptionType.User)}toJSON(){return this.runRequiredValidations(),{...this}}};var B=class{constructor(){n(this,"options")}addBooleanOption(e){return this._sharedAddOptionMethod(e,J)}addUserOption(e){return this._sharedAddOptionMethod(e,W)}addChannelOption(e){return this._sharedAddOptionMethod(e,y)}addRoleOption(e){return this._sharedAddOptionMethod(e,z)}addMentionableOption(e){return this._sharedAddOptionMethod(e,q)}addStringOption(e){return this._sharedAddOptionMethod(e,I)}addIntegerOption(e){return this._sharedAddOptionMethod(e,P)}addNumberOption(e){return this._sharedAddOptionMethod(e,T)}_sharedAddOptionMethod(e,i){let{options:o}=this;d(o);let r=typeof e=="function"?e(new i):e;return C(r,i),o.push(r),this}};var pe=require("discord-api-types/v9"),me=require("ts-mixer");var x=class{constructor(){n(this,"name");n(this,"description");n(this,"options",[])}addSubcommand(e){let{options:i}=this;d(i);let o=typeof e=="function"?e(new c):e;return C(o,c),i.push(o),this}toJSON(){return h(this.name,this.description,this.options),{type:pe.ApplicationCommandOptionType.SubcommandGroup,name:this.name,description:this.description,options:this.options.map(e=>e.toJSON())}}};x=p([(0,me.mix)(f)],x);var c=class{constructor(){n(this,"name");n(this,"description");n(this,"options",[])}toJSON(){return h(this.name,this.description,this.options),{type:pe.ApplicationCommandOptionType.Subcommand,name:this.name,description:this.description,options:this.options.map(e=>e.toJSON())}}};c=p([(0,me.mix)(f,B)],c);var j=class{constructor(){n(this,"name");n(this,"description");n(this,"options",[]);n(this,"defaultPermission")}toJSON(){return h(this.name,this.description,this.options),{name:this.name,description:this.description,options:this.options.map(e=>e.toJSON()),default_permission:this.defaultPermission}}setDefaultPermission(e){return ne(e),Reflect.set(this,"defaultPermission",e),this}addSubcommandGroup(e){let{options:i}=this;d(i);let o=typeof e=="function"?e(new x):e;return C(o,x),i.push(o),this}addSubcommand(e){let{options:i}=this;d(i);let o=typeof e=="function"?e(new c):e;return C(o,c),i.push(o),this}};j=p([(0,Fe.mix)(B,f)],j);var ue={};w(ue,{validateDefaultPermission:()=>ce,validateName:()=>Z,validateRequiredParameters:()=>de,validateType:()=>K});var $=require("zod"),le=require("discord-api-types/v9");function de(t,e){Z(t),K(e)}var At=$.z.string().min(1).max(32).regex(/^( *[\p{L}\p{N}_-]+ *)+$/u);function Z(t){At.parse(t)}var Ot=$.z.union([$.z.literal(le.ApplicationCommandType.User),$.z.literal(le.ApplicationCommandType.Message)]);function K(t){Ot.parse(t)}var St=$.z.boolean();function ce(t){St.parse(t)}var Je=class{constructor(){n(this,"name");n(this,"type");n(this,"defaultPermission")}setName(e){return Z(e),Reflect.set(this,"name",e),this}setType(e){return K(e),Reflect.set(this,"type",e),this}setDefaultPermission(e){return ce(e),Reflect.set(this,"defaultPermission",e),this}toJSON(){return de(this.name,this.type),{name:this.name,type:this.type,default_permission:this.defaultPermission}}};module.exports=Ke(yt);0&&(module.exports={ContextMenuCommandAssertions,ContextMenuCommandBuilder,Embed,EmbedAssertions,Faces,SlashCommandAssertions,SlashCommandBooleanOption,SlashCommandBuilder,SlashCommandChannelOption,SlashCommandIntegerOption,SlashCommandMentionableOption,SlashCommandNumberOption,SlashCommandRoleOption,SlashCommandStringOption,SlashCommandSubcommandBuilder,SlashCommandSubcommandGroupBuilder,SlashCommandUserOption,TimestampStyles,blockQuote,bold,channelMention,codeBlock,formatEmoji,hideLinkEmbed,hyperlink,inlineCode,italic,memberNicknameMention,quote,roleMention,spoiler,strikethrough,time,underscore,userMention});
//# sourceMappingURL=index.js.map