<!DOCTYPE html>

<html lang="en">
    <head>
        <title>jr-handling</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">

        <script src="https://code.jquery.com/jquery-3.6.0.js"></script>
        
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <style>
            :root {
                --dark-bg: rgba(26, 26, 26, 0.95);
                --darker-bg: rgba(20, 20, 20, 0.95);
                --accent: #3498db;
                --text: #ffffff;
                --text-muted: #a0a0a0;
                --border: #2c2c2c;
            }

            * {
                font-family: "Montserrat", sans-serif;
            }

            body {
                background: none !important;
                color: var(--text);
            }

            .handling-container {
                background: none;
                padding: 25px;
                border-radius: 15px;
            }
            
            .modal-content {
                background-color: var(--darker-bg);
                border: 1px solid var(--border);
            }

            .modal-header {
                border-bottom: 1px solid var(--border);
                padding: 1.5rem;
            }

            .modal-header .btn {
                margin: 0 8px;
                padding: 8px 16px;
                font-weight: 500;
            }

            .search-bar {
                padding: 1.5rem;
                margin: 0;
            }

            .form-control {
                background-color: var(--dark-bg);
                border: 1px solid var(--border);
                color: var(--text);
                padding: 12px;
                border-radius: 8px;
            }

            .form-control:focus {
                background-color: var(--dark-bg);
                border-color: var(--accent);
                color: var(--text);
                box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.25);
            }

            .maxheight {
                max-height: 70vh;
                overflow-y: auto;
            }

            .table {
                color: var(--text);
                border-color: var(--border);
            }

            .table-dark {
                background-color: var(--darker-bg);
            }

            .btn {
                font-weight: 500;
                padding: 8px 16px;
                border-radius: 8px;
                transition: all 0.2s ease;
            }

            .btn-primary {
                background-color: var(--accent);
                border: none;
            }

            .btn-primary:hover {
                background-color: #2980b9;
            }

            .btn-secondary {
                background-color: var(--dark-bg);
                border: 1px solid var(--border);
            }

            .tooltip-inner {
                max-width: 300px;
                text-align: left;
                background-color: var(--darker-bg);
                border: 1px solid var(--border);
                padding: 10px;
                font-size: 0.9rem;
                color: var(--text);
            }

            ::-webkit-scrollbar {
                width: 8px;
            }

            ::-webkit-scrollbar-track {
                background: var(--dark-bg);
                border-radius: 8px;
            }

            ::-webkit-scrollbar-thumb {
                background-color: var(--border);
                border-radius: 8px;
                border: 2px solid var(--dark-bg);
            }

            ::-webkit-scrollbar-thumb:hover {
                background-color: var(--text-muted);
            }

            #exportArea {
                width: 100%;
                height: 200px;
                margin-top: 15px;
                display: none;
                background-color: var(--dark-bg);
                color: var(--text);
                border: 1px solid var(--border);
                border-radius: 8px;
                padding: 12px;
            }

            .input-group {
                gap: 8px;
            }

            .input-group > * {
                border-radius: 8px !important;
            }

            .dropdown-menu {
                background-color: var(--dark-bg);
                border: 1px solid var(--border);
                padding: 8px;
                max-height: 300px;
                overflow-y: auto;
            }

            .dropdown-item {
                color: var(--text);
                padding: 8px 16px;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .dropdown-item:hover {
                background-color: var(--darker-bg);
                color: var(--text);
            }

            .dropdown-item input[type="checkbox"] {
                accent-color: var(--accent);
            }

            .flag-dropdown {
                width: 100%;
                margin-top: 8px;
            }

            .flag-dropdown .btn {
                width: 100%;
                text-align: left;
                background: var(--dark-bg);
                border: 1px solid var(--border);
                color: var(--text);
            }

            .flag-dropdown .btn:hover,
            .flag-dropdown .btn:focus {
                background: var(--darker-bg);
                border-color: var(--accent);
            }

        </style>
    </head>
    <body data-bs-theme="dark">
        <div class="modal modal-lg handling-container fade" id="open-handling-modal" tabindex="-1" aria-labelledby="handlingEditor" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="handlingLabel">XA21</h5>
                        <button type="button" class="btn btn-primary" onclick="exportHandling()">Export Handling</button>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="search-bar">
                        <input type="text" id="searchBar" class="form-control" placeholder="Search" onkeyup="searchVehicles()">
                    </div>

                    <div class="modal-body maxheight">
                        <div class="table-responsive">
                            <table class="table table-bordered align-middle">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 40%">Name</th>
                                        <th style="width: 30%">Original</th>
                                        <th style="width: 30%">Current</th>
                                    </tr>
                                </thead>

                                <tbody id="handling-data-accordion">

                                </tbody>
                            </table>
                        </div>
                        <textarea id="exportArea" class="form-control" readonly></textarea>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
        <script defer src="main.js"></script>
    </body>
</html>
