{"cannot_goback": "No se encontró la última ubicación. Necesitas teletransportarte a algún lugar primero", "no_marker": "No has establecido ningún marcador", "command_tpm": "%s Teletransportarse al marcador", "command_noclip": "%s Alternar noclip", "command_openui": "%s <PERSON>brir <PERSON>", "command_weather_notfound": "Intentando establecer un tipo de clima no válido: %s", "teleport_invalid_coords": "Intentando teletransportar al jugador a un tipo de coordenadas inválidas", "model_doesnt_exist": "El modelo %s no existe", "copied_coords_clipboard": "Coordenadas copiadas al portapapeles", "copied_model_clipboard": "Hash del modelo copiado al portapapeles", "press_escape_exit": "Presiona 'Escape' para salir del modo de edición", "custom_location_created": "Ubicación personalizada creada exitosamente", "vehicle_upgraded": "¡Vehículo mejorado exitosamente!", "weapon_gave": "Acabas de recibir un arma", "weapon_cant_carry": "No puedes recibir esta arma", "max_health_set": "La salud máxima se estableció exitosamente", "entity_cant_be_loaded": "La entidad no puede ser cargada...", "entity_doesnt_exist": "La entidad no existe", "entity_deleted": "Entidad eliminada exitosamente", "teleport_success": "Teletransportado exitosamente. Usa /goback para volver a la última ubicación", "ui_home": "<PERSON><PERSON>o", "ui_world": "Mundo", "ui_exit": "Salir", "ui_copy_coords": "<PERSON><PERSON><PERSON>ord<PERSON>", "ui_copied_coords": "Coordenadas copiadas", "ui_copy_name": "Copiar nombre", "ui_copied_name": "Nombre copiado", "ui_copy_hash": "Copiar hash", "ui_copied_hash": "Hash copiado", "ui_name": "Nombre", "ui_hash": "Hash", "ui_coords": "<PERSON><PERSON><PERSON><PERSON>", "ui_heading": "R<PERSON>bo", "ui_interior_id": "ID del interior", "ui_current_room": "Sala actual", "ui_teleport": "Teletransportar", "ui_not_in_interior": "No estás dentro de ningún interior", "ui_no_last_location": "Aún no te has teletransportado a ninguna ubicación", "ui_current_coords": "Coordenadas actuales", "ui_set_coords": "<PERSON><PERSON><PERSON>", "ui_save_location": "Guardar como ubicación", "ui_last_location": "Última ubicación", "ui_current_interior": "Interior actual", "ui_quick_actions": "Acciones rápidas", "ui_clean_zone": "Limpiar zona", "ui_clean_ped": "<PERSON><PERSON><PERSON> ped", "ui_upgrade_vehicle": "Act<PERSON><PERSON><PERSON>", "ui_repair_vehicle": "<PERSON><PERSON><PERSON>", "ui_delete_vehicle": "Eliminar veh<PERSON>lo", "ui_set_sunny_day": "E<PERSON>cer día soleado", "ui_spawn_vehicle": "<PERSON><PERSON>", "ui_max_health": "<PERSON><PERSON> m<PERSON>xi<PERSON>", "ui_time_freeze": "Tiempo congelado", "ui_time_not_freeze": "Tiempo no congelado", "ui_time": "Tiempo", "ui_sync": "Sincronizar", "ui_freeze_time": "<PERSON><PERSON><PERSON> tiempo", "ui_weather": "Clima", "ui_choose_weather": "Escoger un tipo de clima", "ui_current_weather": "¿Clima actual?", "ui_freeze_weather": "Congelar clima", "ui_interior": "Interior", "ui_room_count": "Cantidad de salas", "ui_portal_count": "Cantidad de portales", "ui_portals": "Portales", "ui_infos": "Información", "ui_fill_portals": "<PERSON><PERSON><PERSON>", "ui_outline_portals": "Contorno", "ui_corcers_portals": "Esquinas", "ui_flag": "Bandera", "ui_room_from": "Sala desde", "ui_room_to": "<PERSON>a hasta", "ui_index": "<PERSON><PERSON><PERSON>", "ui_timecycle": "Ciclo de tiempo", "ui_no_timecycle_found": "No se encontró ciclo de tiempo", "ui_object_spawner": "Generador de objetos", "ui_locations": "Ubicaciones", "ui_snap_to_ground": "Ajustar al suelo", "ui_duplicate": "Duplicar", "ui_no_location_found": "No se encontró ninguna ubicación", "ui_goto": "Ir a", "ui_show_custom_locations": "Mostrar ubicaciones personalizadas", "ui_show_vanilla_locations": "Mostrar ubicaciones originales", "ui_create_custom_location": "Crear ubicación personalizada", "ui_search": "Buscar", "ui_rename": "Renombrar", "ui_delete": "Eliminar", "ui_vanilla": "Original", "ui_custom": "Personalizado", "ui_peds": "Peds", "ui_no_ped_found": "No se encontró ningún ped", "ui_set_by_name": "<PERSON><PERSON><PERSON> por nombre", "ui_set_ped": "Aplicar", "ui_vehicles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ui_spawn": "Generar", "ui_spawn_by_name": "Generar por nombre", "ui_no_vehicle_found": "No se encontró ningún vehículo", "ui_weapons": "Armas", "ui_give_weapon_by_name": "Dar arma por nombre", "ui_give_weapon": "Dar", "ui_no_weapon_found": "No se encontró ninguna arma", "ui_set_coords_as_string": "Establecer como cadena", "ui_set_coords_separate": "Establecer como valores separados", "ui_confirm": "Confirmar", "ui_cancel": "<PERSON><PERSON><PERSON>", "ui_location_name": "Nombre de la ubicación", "ui_create_location_description": "Guardar tus coordenadas y rumbo actuales", "ui_add_entity": "Agregar una nueva entidad", "ui_add_entity_description": "Ingresa el nombre de la entidad", "ui_delete_all_entities": "¿Eliminar todas las entidades generadas?", "ui_amount": "Cantidad", "ui_portal_flag_1": "Desactiva renderizado exterior", "ui_portal_flag_2": "Desactiva renderizado interior", "ui_portal_flag_4": "<PERSON><PERSON><PERSON><PERSON>", "ui_portal_flag_8": "Brillo adicional", "ui_portal_flag_16": "Desconocido 16", "ui_portal_flag_32": "Usar LOD exterior", "ui_portal_flag_64": "Ocultar cuando la puerta está cerrada", "ui_portal_flag_128": "Desconocido 128", "ui_portal_flag_256": "Espejar portales exteriores", "ui_portal_flag_512": "Desconocido 512", "ui_portal_flag_1024": "Espejar entidades en el limbo", "ui_portal_flag_2048": "Desconocido 2048", "ui_portal_flag_4096": "Desconocido 4096", "ui_portal_flag_8192": "Desactivar farclipping", "ui_update_warning": "¡Actualización disponible!", "ui_github": "Abrir repositorio en Github", "ui_discord": "Únete al Discord de Dolu", "ui_audio": "Audio", "ui_static_emitters": "Emisores está<PERSON>", "ui_draw_static_emitters": "Mostrar emisores estáticos", "ui_draw_distance": "Distancia de dibujo", "ui_closest_emitter_info": "Información del emisor más cercano", "ui_refresh": "Actualizar", "ui_distance": "Distancia", "ui_meters": "metros", "ui_flags": "Banderas", "ui_room": "Sala", "ui_radio_station": "Estación de radio", "ui_copied_rotation": "Rotación copiada", "ui_copy_rotation": "Copiar rotación"}