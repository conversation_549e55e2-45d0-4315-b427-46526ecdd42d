{"version": 3, "file": "index.umd.js", "sources": ["../src/lib/AsyncQueue.ts"], "sourcesContent": ["/**\n * The AsyncQueue class used to sequentialize burst requests\n */\nexport class AsyncQueue {\n\t/**\n\t * The remaining amount of queued promises\n\t */\n\tpublic get remaining(): number {\n\t\treturn this.promises.length;\n\t}\n\n\t/**\n\t * The promises array\n\t */\n\tprivate promises: InternalAsyncQueueDeferredPromise[] = [];\n\n\t/**\n\t * Waits for last promise and queues a new one\n\t * @example\n\t * ```typescript\n\t * const queue = new AsyncQueue();\n\t * async function request(url, options) {\n\t *     await queue.wait();\n\t *     try {\n\t *         const result = await fetch(url, options);\n\t *         // Do some operations with 'result'\n\t *     } finally {\n\t *         // Remove first entry from the queue and resolve for the next entry\n\t *         queue.shift();\n\t *     }\n\t * }\n\t *\n\t * request(someUrl1, someOptions1); // Will call fetch() immediately\n\t * request(someUrl2, someOptions2); // Will call fetch() after the first finished\n\t * request(someUrl3, someOptions3); // Will call fetch() after the second finished\n\t * ```\n\t */\n\tpublic wait(): Promise<void> {\n\t\tconst next = this.promises.length ? this.promises[this.promises.length - 1].promise : Promise.resolve();\n\t\tlet resolve: () => void;\n\t\tconst promise = new Promise<void>((res) => {\n\t\t\tresolve = res;\n\t\t});\n\n\t\tthis.promises.push({\n\t\t\tresolve: resolve!,\n\t\t\tpromise\n\t\t});\n\n\t\treturn next;\n\t}\n\n\t/**\n\t * Frees the queue's lock for the next item to process\n\t */\n\tpublic shift(): void {\n\t\tconst deferred = this.promises.shift();\n\t\tif (typeof deferred !== 'undefined') deferred.resolve();\n\t}\n}\n\n/**\n * @internal\n */\ninterface InternalAsyncQueueDeferredPromise {\n\tresolve(): void;\n\tpromise: Promise<void>;\n}\n"], "names": [], "mappings": ";;;;;;CAAA;;;OAGa,UAAU;KAAvB;;;;SAWC;;;;oBAAwD,EAAE;YAAC;MA6C3D;;;;KApDA,IAAW,SAAS;SACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;MAC5B;;;;;;;;;;;;;;;;;;;;;;KA4BM,IAAI;SACV,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;SACxG,IAAI,OAAmB,CAAC;SACxB,MAAM,OAAO,GAAG,IAAI,OAAO,CAAO,CAAC,GAAG;aACrC,OAAO,GAAG,GAAG,CAAC;UACd,CAAC,CAAC;SAEH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;aAClB,OAAO,EAAE,OAAQ;aACjB,OAAO;UACP,CAAC,CAAC;SAEH,OAAO,IAAI,CAAC;MACZ;;;;KAKM,KAAK;SACX,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;SACvC,IAAI,OAAO,QAAQ,KAAK,WAAW;aAAE,QAAQ,CAAC,OAAO,EAAE,CAAC;MACxD;;;;;;;;;;;"}