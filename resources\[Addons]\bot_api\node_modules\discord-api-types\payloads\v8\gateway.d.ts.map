{"version": 3, "file": "gateway.d.ts", "sourceRoot": "", "sources": ["gateway.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAC/C,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AACxC,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,QAAQ,CAAC;AAEtC;;GAEG;AACH,MAAM,WAAW,cAAc;IAC9B;;OAEG;IACH,GAAG,EAAE,MAAM,CAAC;CACZ;AAED;;GAEG;AACH,MAAM,WAAW,iBAAkB,SAAQ,cAAc;IACxD;;;;OAIG;IACH,MAAM,EAAE,MAAM,CAAC;IACf;;;;OAIG;IACH,mBAAmB,EAAE,2BAA2B,CAAC;CACjD;AAED;;GAEG;AACH,MAAM,WAAW,2BAA2B;IAC3C;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAClB;;OAEG;IACH,WAAW,EAAE,MAAM,CAAC;IACpB;;OAEG;IACH,eAAe,EAAE,MAAM,CAAC;CACxB;AAED;;GAEG;AACH,MAAM,WAAW,qBAAqB;IACrC;;;;;;;OAOG;IACH,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC7C;;OAEG;IACH,QAAQ,EAAE,SAAS,CAAC;IACpB;;OAEG;IACH,MAAM,CAAC,EAAE,oBAAoB,CAAC;IAC9B;;;;OAIG;IACH,UAAU,CAAC,EAAE,eAAe,EAAE,CAAC;IAC/B;;;;OAIG;IACH,aAAa,CAAC,EAAE,2BAA2B,CAAC;CAC5C;AAED,0BAAkB,oBAAoB;IACrC,MAAM,WAAW;IACjB,YAAY,QAAQ;IACpB,IAAI,SAAS;IACb;;OAEG;IACH,SAAS,cAAc;IACvB,OAAO,YAAY;CACnB;AAED;;GAEG;AACH,MAAM,WAAW,2BAA2B;IAC3C;;OAEG;IACH,OAAO,CAAC,EAAE,oBAAoB,CAAC;IAC/B;;OAEG;IACH,MAAM,CAAC,EAAE,oBAAoB,CAAC;IAC9B;;OAEG;IACH,GAAG,CAAC,EAAE,oBAAoB,CAAC;CAC3B;AAED;;GAEG;AACH,MAAM,WAAW,eAAe;IAC/B;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IACX;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;;;OAIG;IACH,IAAI,EAAE,YAAY,CAAC;IACnB;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACpB;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IACnB;;OAEG;IACH,UAAU,CAAC,EAAE,yBAAyB,CAAC;IACvC,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,QAAQ,CAAC,EAAE,gBAAgB,CAAC;IAC5B;;OAEG;IACH,cAAc,CAAC,EAAE,SAAS,CAAC;IAC3B;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB;;;;OAIG;IACH,KAAK,CAAC,EAAE,oBAAoB,CAAC;IAC7B,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB;;;;OAIG;IACH,KAAK,CAAC,EAAE,oBAAoB,CAAC;IAC7B;;;;OAIG;IACH,MAAM,CAAC,EAAE,qBAAqB,CAAC;IAC/B;;;;OAIG;IACH,OAAO,CAAC,EAAE,sBAAsB,CAAC;IACjC;;OAEG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB;;;;;;OAMG;IACH,KAAK,CAAC,EAAE,aAAa,CAAC;IACtB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,qBAAqB,EAAE,CAAC;CAC7C;AAED,oBAAY,gBAAgB;IAC3B,OAAO,YAAY;IACnB,OAAO,YAAY;IACnB,IAAI,SAAS;CACb;AAED;;GAEG;AACH,0BAAkB,YAAY;IAC7B;;OAEG;IACH,IAAI,IAAA;IACJ;;OAEG;IACH,SAAS,IAAA;IACT;;OAEG;IACH,SAAS,IAAA;IACT;;OAEG;IACH,QAAQ,IAAA;IACR;;OAEG;IACH,MAAM,IAAA;IACN;;OAEG;IACH,SAAS,IAAA;CACT;AAED;;GAEG;AACH,MAAM,WAAW,yBAAyB;IACzC;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;CACb;AAED;;GAEG;AACH,oBAAY,oBAAoB,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,GAAG,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAEvG;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACpC;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ;;OAEG;IACH,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;CAChD;AAED;;GAEG;AACH,oBAAY,qBAAqB,GAAG,OAAO,CAC1C,MAAM,CAAC,aAAa,GAAG,YAAY,GAAG,aAAa,GAAG,YAAY,EAAE,MAAM,CAAC,CAC3E,CAAC;AAEF;;GAEG;AACH,oBAAY,sBAAsB,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,GAAG,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;AAE5F;;GAEG;AACH,0BAAkB,aAAa;IAC9B,QAAQ,IAAS;IACjB,IAAI,IAAS;IACb,QAAQ,IAAS;IACjB,WAAW,IAAS;IACpB,IAAI,KAAS;IACb,IAAI,KAAS;IACb,mBAAmB,KAAS;IAC5B,wBAAwB,MAAS;IACjC,QAAQ,MAAS;CACjB;AAED,MAAM,WAAW,qBAAqB;IACrC;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,GAAG,EAAE,MAAM,CAAC;CACZ"}